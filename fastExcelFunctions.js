/**
 * Fast Excel Functions - Optimized Performance
 * <AUTHOR> Team <<EMAIL>>
 * @license  MIT
 * @version  1.1.0
 * 
 * High-performance Excel generation functions with parallel processing
 */

// دالة محسنة وسريعة لتحميل Excel للفواتير
async function downloadExcelSummaryFast(paramList) {
    try {
        $('#downloadModal').modal('hide');
        const totalCount = paramList.length;
        
        if (totalCount === 0) return;
        
        const btn = $(".downloadAllBtn");
        $(btn).addClass("disabled");
        $("#cancelDownloadBtn").show();
        
        // تحسين متقدم: معالجة متوازية ذكية مع تكيف ديناميكي
        const MAX_CONCURRENT = Math.min(10, Math.max(5, Math.ceil(totalCount / 10))); // 5-10 طلبات حسب العدد
        const CHUNK_SIZE = Math.min(50, Math.max(10, Math.ceil(totalCount / 5))); // حجم القطعة الديناميكي

        console.log(`Optimized settings: ${MAX_CONCURRENT} concurrent, ${CHUNK_SIZE} chunk size`);

        const results = [];
        let processedCount = 0;

        // معالجة بالقطع الكبيرة مع طلبات متوازية
        for (let chunkStart = 0; chunkStart < paramList.length; chunkStart += CHUNK_SIZE) {
            if (isCancellationRequested) break;

            const chunk = paramList.slice(chunkStart, chunkStart + CHUNK_SIZE);
            const chunkResults = [];

            // معالجة القطعة بطلبات متوازية
            for (let i = 0; i < chunk.length; i += MAX_CONCURRENT) {
                if (isCancellationRequested) break;

                const batch = chunk.slice(i, i + MAX_CONCURRENT);
                processedCount += batch.length;

                $(".downloadAllBtnText").html(`Loading ${processedCount}/${totalCount} (${Math.round(processedCount/totalCount*100)}%)`);

                const batchPromises = batch.map(async (param) => {
                    try {
                        const e = param.source || param;
                        const startTime = performance.now();
                        const result = await getDocumentDetails(e.uuid);
                        const endTime = performance.now();

                        // تسجيل الأداء للمراقبة
                        if (endTime - startTime > 5000) {
                            console.warn(`Slow request: ${e.uuid} took ${Math.round(endTime - startTime)}ms`);
                        }

                        return result;
                    } catch (error) {
                        console.warn('Failed to load invoice:', param.uuid || param.id, error.message);
                        return null;
                    }
                });

                const batchResults = await Promise.all(batchPromises);
                chunkResults.push(...batchResults.filter(r => r !== null));

                // تأخير قصير لتجنب إرهاق الخادم
                if (i + MAX_CONCURRENT < chunk.length) {
                    await new Promise(resolve => setTimeout(resolve, 100));
                }
            }

            results.push(...chunkResults);

            // تحديث التقدم
            const progress = Math.round((chunkStart + CHUNK_SIZE) / paramList.length * 100);
            console.log(`Chunk completed: ${chunkResults.length}/${chunk.length} items, Progress: ${progress}%`);
        }

        if (!isCancellationRequested && results.length > 0) {
            $(".downloadAllBtnText").html("Creating Excel...");

            // تحسين تلقائي حسب حجم البيانات
            if (results.length > 500) {
                console.log(`Large dataset detected (${results.length} items), using optimized processing...`);
                await createExcelFileUltraFast(results);
            } else {
                await createExcelFileFast(results);
            }
        }
        
    } catch (error) {
        console.error('Error in downloadExcelSummaryFast:', error);
    } finally {
        $(".downloadAllBtnText").html("Download All");
        $(".downloadAllBtn").removeClass("disabled");
        $("#cancelDownloadBtn").hide();
        isCancellationRequested = false;
    }
}

// دالة سريعة لإنشاء Excel
async function createExcelFileFast(invoiceData) {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet("جميع الفواتير");
    
    worksheet.views = [{ rightToLeft: true, rtl: true, activeCell: 'A1', state: "frozen", ySplit: 1 }];
    
    // تحديد الأعمدة مسبقاً
    const columns = [
        { header: 'مسلسل', key: 'index', width: 10 },
        { header: 'نوع المستند', key: 'type', width: 12 },
        { header: 'الحالة', key: 'status', width: 10 },
        { header: 'تاريخ الإصدار', key: 'dateIssued', width: 13 },
        { header: 'قيمة الفاتورة', key: 'netAmount', width: 12 },
        { header: 'إجمالى الفاتورة', key: 'totalAmount', width: 14 },
        { header: 'الرقم الداخلى', key: 'invoiceNumber', width: 23 },
        { header: 'إسم البائع', key: 'issuerName', width: 32 },
        { header: 'إسم المشترى', key: 'receiverName', width: 32 },
        { header: 'التوقيع الرقمي', key: 'digitalSignature', width: 25 },
        { header: 'حالة التوقيع', key: 'signatureStatus', width: 15 }
    ];
    
    worksheet.columns = columns;
    
    // معالجة البيانات بشكل مجمع
    const rows = invoiceData.map((doc, index) => ({
        index: index + 1,
        type: doc.documentTypeNameAr || doc.documentTypeNameSecondaryLang || '',
        status: doc.status || '',
        dateIssued: doc.issueDate || doc.dateTimeIssued || '',
        netAmount: doc.netAmount || 0,
        totalAmount: doc.totalAmount || 0,
        invoiceNumber: doc.internalId || '',
        issuerName: doc.issuerName || '',
        receiverName: doc.receiverName || '',
        digitalSignature: doc.signatures && doc.signatures.length > 0 ? 'موقع رقمياً' : 'غير موقع',
        signatureStatus: doc.signatures && doc.signatures.length > 0 ? 'صالح' : 'غير متوفر'
    }));
    
    // إضافة جميع الصفوف دفعة واحدة
    worksheet.addRows(rows);
    
    // إنشاء وحفظ الملف باستخدام الدالة المحسنة
    await generateExcelUltraFast(rows, "جميع الفواتير", columns, "eInvoices_Fast.xlsx");
}

// دالة محسنة للمجموعات الكبيرة من الفواتير
async function createExcelFileUltraFast(invoiceData) {
    console.log(`Creating ultra-fast Excel for ${invoiceData.length} invoices...`);

    // تحديد الأعمدة الأساسية فقط للسرعة
    const columns = [
        { header: 'مسلسل', key: 'index', width: 8 },
        { header: 'نوع المستند', key: 'type', width: 10 },
        { header: 'الحالة', key: 'status', width: 8 },
        { header: 'تاريخ الإصدار', key: 'dateIssued', width: 12 },
        { header: 'قيمة الفاتورة', key: 'netAmount', width: 12 },
        { header: 'إجمالى الفاتورة', key: 'totalAmount', width: 12 },
        { header: 'الرقم الداخلى', key: 'invoiceNumber', width: 20 },
        { header: 'إسم البائع', key: 'issuerName', width: 25 },
        { header: 'إسم المشترى', key: 'receiverName', width: 25 },
        { header: 'التوقيع الرقمي', key: 'digitalSignature', width: 15 },
        { header: 'حالة التوقيع', key: 'signatureStatus', width: 12 }
    ];

    // معالجة البيانات بشكل مبسط وسريع
    const rows = invoiceData.map((doc, index) => {
        // استخراج البيانات الأساسية فقط
        const dateIssued = doc.issueDate || doc.dateTimeIssued || '';
        const formattedDate = dateIssued ? new Date(dateIssued).toLocaleDateString('ar-EG') : '';

        return {
            index: index + 1,
            type: (doc.documentTypeNameAr || doc.documentTypeNameSecondaryLang || '').substring(0, 15),
            status: (doc.status || '').substring(0, 10),
            dateIssued: formattedDate,
            netAmount: parseFloat(doc.netAmount) || 0,
            totalAmount: parseFloat(doc.totalAmount) || 0,
            invoiceNumber: (doc.internalId || '').substring(0, 25),
            issuerName: (doc.issuerName || '').substring(0, 30),
            receiverName: (doc.receiverName || '').substring(0, 30),
            digitalSignature: doc.signatures && doc.signatures.length > 0 ? 'موقع' : 'غير موقع',
            signatureStatus: doc.signatures && doc.signatures.length > 0 ? 'صالح' : 'غير متوفر'
        };
    });

    // إنشاء Excel مع تحسينات قصوى
    await generateExcelUltraFast(rows, "جميع الفواتير", columns, "eInvoices_UltraFast.xlsx", {
        compression: true,
        batchSize: 2000
    });
}

// دالة محسنة وسريعة لتحميل Excel للإيصالات
async function downloadExcelForReceiptsFast(paramList) {
    try {
        $('#downloadModal').modal('hide');
        const totalCount = paramList.length;
        
        if (totalCount === 0) return;
        
        const btn = $(".downloadAllBtn");
        $(btn).addClass("disabled");
        $("#cancelDownloadBtn").show();
        
        // تحسين متقدم: معالجة متوازية ذكية للإيصالات
        const MAX_CONCURRENT = Math.min(10, Math.max(5, Math.ceil(totalCount / 10)));
        const CHUNK_SIZE = Math.min(50, Math.max(10, Math.ceil(totalCount / 5)));

        console.log(`Receipts optimized settings: ${MAX_CONCURRENT} concurrent, ${CHUNK_SIZE} chunk size`);

        const results = [];
        let processedCount = 0;

        // معالجة بالقطع الكبيرة مع طلبات متوازية
        for (let chunkStart = 0; chunkStart < paramList.length; chunkStart += CHUNK_SIZE) {
            if (isCancellationRequested) break;

            const chunk = paramList.slice(chunkStart, chunkStart + CHUNK_SIZE);
            const chunkResults = [];

            // معالجة القطعة بطلبات متوازية
            for (let i = 0; i < chunk.length; i += MAX_CONCURRENT) {
                if (isCancellationRequested) break;

                const batch = chunk.slice(i, i + MAX_CONCURRENT);
                processedCount += batch.length;

                $(".downloadAllBtnText").html(`Loading ${processedCount}/${totalCount} (${Math.round(processedCount/totalCount*100)}%)`);

                const batchPromises = batch.map(async (param) => {
                    try {
                        const e = param.source || param;
                        const startTime = performance.now();
                        const result = await getDocumentDetails(e.uuid);
                        const endTime = performance.now();

                        if (endTime - startTime > 5000) {
                            console.warn(`Slow receipt request: ${e.uuid} took ${Math.round(endTime - startTime)}ms`);
                        }

                        return result;
                    } catch (error) {
                        console.warn('Failed to load receipt:', param.uuid || param.id, error.message);
                        return null;
                    }
                });

                const batchResults = await Promise.all(batchPromises);
                chunkResults.push(...batchResults.filter(r => r !== null));

                // تأخير قصير لتجنب إرهاق الخادم
                if (i + MAX_CONCURRENT < chunk.length) {
                    await new Promise(resolve => setTimeout(resolve, 100));
                }
            }

            results.push(...chunkResults);

            // تحديث التقدم
            const progress = Math.round((chunkStart + CHUNK_SIZE) / paramList.length * 100);
            console.log(`Receipt chunk completed: ${chunkResults.length}/${chunk.length} items, Progress: ${progress}%`);
        }

        if (!isCancellationRequested && results.length > 0) {
            $(".downloadAllBtnText").html("Creating Excel...");
            await createReceiptExcelFast(results);
        }
        
    } catch (error) {
        console.error('Error in downloadExcelForReceiptsFast:', error);
    } finally {
        $(".downloadAllBtnText").html("Download All");
        $(".downloadAllBtn").removeClass("disabled");
        $("#cancelDownloadBtn").hide();
        isCancellationRequested = false;
    }
}

// دالة سريعة لإنشاء Excel للإيصالات
async function createReceiptExcelFast(receiptData) {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet("جميع الإيصالات");
    
    worksheet.views = [{ rightToLeft: true, rtl: true, activeCell: 'A1', state: "frozen", ySplit: 1 }];
    
    // تحديد الأعمدة مسبقاً
    const columns = [
        { header: 'مسلسل', key: 'index', width: 10 },
        { header: 'نوع المستند', key: 'type', width: 12 },
        { header: 'الحالة', key: 'status', width: 10 },
        { header: 'تاريخ الإصدار', key: 'dateIssued', width: 13 },
        { header: 'قيمة الإيصال', key: 'netAmount', width: 12 },
        { header: 'إجمالى الإيصال', key: 'totalAmount', width: 14 },
        { header: 'الرقم الداخلى', key: 'receiptNumber', width: 23 },
        { header: 'إسم البائع', key: 'issuerName', width: 32 },
        { header: 'إسم المشترى', key: 'receiverName', width: 32 },
        { header: 'التوقيع الرقمي', key: 'digitalSignature', width: 25 },
        { header: 'حالة التوقيع', key: 'signatureStatus', width: 15 }
    ];
    
    worksheet.columns = columns;
    
    // معالجة البيانات بشكل مجمع
    const rows = receiptData.map((doc, index) => ({
        index: index + 1,
        type: doc.documentTypeNameAr || doc.documentTypeNameSecondaryLang || '',
        status: doc.status || '',
        dateIssued: doc.issueDate || doc.dateTimeIssued || '',
        netAmount: doc.netAmount || 0,
        totalAmount: doc.totalAmount || 0,
        receiptNumber: doc.internalId || '',
        issuerName: doc.issuerName || '',
        receiverName: doc.receiverName || '',
        digitalSignature: doc.signatures && doc.signatures.length > 0 ? 'موقع رقمياً' : 'غير موقع',
        signatureStatus: doc.signatures && doc.signatures.length > 0 ? 'صالح' : 'غير متوفر'
    }));
    
    // إضافة جميع الصفوف دفعة واحدة
    worksheet.addRows(rows);
    
    // إنشاء وحفظ الملف باستخدام الدالة المحسنة
    await generateExcelUltraFast(rows, "جميع الإيصالات", columns, "eReceipts_Fast.xlsx");
}

// دالة متقدمة لتحسين الأداء - تحميل متوازي ذكي
async function loadDocumentsBatchAdvanced(paramList, options = {}) {
    const {
        maxConcurrent = Math.min(15, Math.max(5, Math.ceil(paramList.length / 8))),
        chunkSize = Math.min(100, Math.max(20, Math.ceil(paramList.length / 3))),
        retryAttempts = 2,
        timeout = 10000
    } = options;

    console.log(`Advanced batch loading: ${maxConcurrent} concurrent, ${chunkSize} chunk size, ${retryAttempts} retries`);

    const results = [];
    let processedCount = 0;
    let failedCount = 0;

    // إحصائيات الأداء
    const performanceStats = {
        startTime: performance.now(),
        requestTimes: [],
        errors: []
    };

    for (let chunkStart = 0; chunkStart < paramList.length; chunkStart += chunkSize) {
        if (isCancellationRequested) break;

        const chunk = paramList.slice(chunkStart, chunkStart + chunkSize);
        const chunkResults = [];

        // معالجة القطعة بطلبات متوازية مع إعادة المحاولة
        for (let i = 0; i < chunk.length; i += maxConcurrent) {
            if (isCancellationRequested) break;

            const batch = chunk.slice(i, i + maxConcurrent);
            processedCount += batch.length;

            const progress = Math.round(processedCount / paramList.length * 100);
            $(".downloadAllBtnText").html(`Loading ${processedCount}/${paramList.length} (${progress}%) - Failed: ${failedCount}`);

            const batchPromises = batch.map(async (param) => {
                const e = param.source || param;

                for (let attempt = 0; attempt <= retryAttempts; attempt++) {
                    try {
                        const startTime = performance.now();

                        // إضافة timeout للطلبات
                        const timeoutPromise = new Promise((_, reject) =>
                            setTimeout(() => reject(new Error('Request timeout')), timeout)
                        );

                        const result = await Promise.race([
                            getDocumentDetails(e.uuid),
                            timeoutPromise
                        ]);

                        const endTime = performance.now();
                        const requestTime = endTime - startTime;
                        performanceStats.requestTimes.push(requestTime);

                        if (requestTime > 8000) {
                            console.warn(`Very slow request: ${e.uuid} took ${Math.round(requestTime)}ms`);
                        }

                        return result;

                    } catch (error) {
                        if (attempt === retryAttempts) {
                            failedCount++;
                            performanceStats.errors.push({
                                uuid: e.uuid,
                                error: error.message,
                                attempts: attempt + 1
                            });
                            console.warn(`Failed after ${attempt + 1} attempts:`, e.uuid, error.message);
                            return null;
                        } else {
                            console.log(`Retry ${attempt + 1} for:`, e.uuid);
                            await new Promise(resolve => setTimeout(resolve, 1000 * (attempt + 1))); // تأخير متزايد
                        }
                    }
                }
                return null;
            });

            const batchResults = await Promise.all(batchPromises);
            chunkResults.push(...batchResults.filter(r => r !== null));

            // تأخير تكيفي حسب الأداء
            const avgRequestTime = performanceStats.requestTimes.length > 0
                ? performanceStats.requestTimes.reduce((a, b) => a + b, 0) / performanceStats.requestTimes.length
                : 1000;

            const adaptiveDelay = Math.min(500, Math.max(50, avgRequestTime / 10));
            if (i + maxConcurrent < chunk.length) {
                await new Promise(resolve => setTimeout(resolve, adaptiveDelay));
            }
        }

        results.push(...chunkResults);

        // إحصائيات القطعة
        const chunkProgress = Math.round((chunkStart + chunkSize) / paramList.length * 100);
        console.log(`Chunk ${Math.floor(chunkStart/chunkSize) + 1} completed: ${chunkResults.length}/${chunk.length} items (${chunkProgress}%)`);
    }

    // إحصائيات نهائية
    const totalTime = performance.now() - performanceStats.startTime;
    const avgRequestTime = performanceStats.requestTimes.length > 0
        ? performanceStats.requestTimes.reduce((a, b) => a + b, 0) / performanceStats.requestTimes.length
        : 0;

    console.log(`Batch loading completed:`, {
        total: paramList.length,
        successful: results.length,
        failed: failedCount,
        totalTime: Math.round(totalTime),
        avgRequestTime: Math.round(avgRequestTime),
        requestsPerSecond: Math.round(paramList.length / (totalTime / 1000))
    });

    return results;
}

// دالة متقدمة لإنشاء Excel بسرعة عالية مع تحسينات
async function generateExcelUltraFast(data, worksheetName, columns, filename, options = {}) {
    const startTime = performance.now();

    try {
        console.log(`Starting Excel generation for ${data.length} rows...`);

        const workbook = new ExcelJS.Workbook();

        // تحسين إعدادات الـ workbook
        workbook.creator = 'Oditlz ETA Tool';
        workbook.lastModifiedBy = 'Oditlz Team';
        workbook.created = new Date();
        workbook.modified = new Date();

        const worksheet = workbook.addWorksheet(worksheetName);

        // تحسين إعدادات الـ worksheet
        worksheet.views = [{
            rightToLeft: true,
            rtl: true,
            activeCell: 'A1',
            state: "frozen",
            ySplit: 1,
            showGridLines: true,
            showRowColHeaders: true
        }];

        // تحسين الأعمدة
        worksheet.columns = columns.map(col => ({
            ...col,
            style: {
                font: { name: 'Calibri', size: 11 },
                alignment: { horizontal: 'right', vertical: 'middle' }
            }
        }));

        // تحسين إضافة البيانات - معالجة على دفعات
        const BATCH_SIZE = 1000;
        let addedRows = 0;

        for (let i = 0; i < data.length; i += BATCH_SIZE) {
            const batch = data.slice(i, i + BATCH_SIZE);
            worksheet.addRows(batch);
            addedRows += batch.length;

            // تحديث التقدم
            if (data.length > 1000) {
                const progress = Math.round(addedRows / data.length * 100);
                $(".downloadAllBtnText").html(`Creating Excel... ${progress}%`);
            }

            // تأخير قصير للسماح للواجهة بالتحديث
            if (i + BATCH_SIZE < data.length) {
                await new Promise(resolve => setTimeout(resolve, 10));
            }
        }

        // تحسين تنسيق الرأس
        const headerRow = worksheet.getRow(1);
        headerRow.font = { name: 'Calibri', size: 12, bold: true };
        headerRow.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'FFE6E6FA' }
        };
        headerRow.alignment = { horizontal: 'center', vertical: 'middle' };

        // إضافة فلاتر تلقائية
        if (data.length > 0) {
            worksheet.autoFilter = {
                from: 'A1',
                to: { row: data.length + 1, column: columns.length }
            };
        }

        // تحسين عرض الأعمدة تلقائياً (للأعمدة الصغيرة فقط)
        columns.forEach((col, index) => {
            const column = worksheet.getColumn(index + 1);
            if (col.width && col.width < 20) {
                column.width = col.width;
            }
        });

        console.log(`Excel structure created, generating buffer...`);
        $(".downloadAllBtnText").html("Finalizing Excel...");

        // إنشاء الملف مع ضغط محسن
        const buffer = await workbook.xlsx.writeBuffer({
            compression: 'DEFLATE',
            compressionLevel: 6
        });

        const blob = new Blob([buffer], {
            type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        });

        const endTime = performance.now();
        const totalTime = Math.round(endTime - startTime);

        console.log(`Excel generation completed:`, {
            rows: data.length,
            columns: columns.length,
            fileSize: Math.round(blob.size / 1024) + ' KB',
            totalTime: totalTime + ' ms',
            rowsPerSecond: Math.round(data.length / (totalTime / 1000))
        });

        saveAs(blob, filename);

    } catch (error) {
        console.error('Error in generateExcelUltraFast:', error);
        throw error;
    }
}

// دالة مساعدة لإنشاء Excel بسرعة (للتوافق مع الكود القديم)
async function generateExcelFast(data, worksheetName, columns, filename) {
    return await generateExcelUltraFast(data, worksheetName, columns, filename);
}
