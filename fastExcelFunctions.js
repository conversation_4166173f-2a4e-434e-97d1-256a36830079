/**
 * Fast Excel Functions - Simple and Reliable
 * <AUTHOR> Team <<EMAIL>>
 * @license  MIT
 * @version  1.0.0
 *
 * Simple Excel generation functions
 */

// فحص بسيط للمكتبات
(function() {
    console.log('Fast Excel Functions loading...');

    setTimeout(() => {
        if (typeof ExcelJS === 'undefined') {
            console.warn('ExcelJS not detected');
        } else {
            console.log('ExcelJS ready');
        }

        if (typeof saveAs === 'undefined') {
            console.warn('saveAs not detected');
        } else {
            console.log('saveAs ready');
        }
    }, 1000);
})();

// دالة بسيطة للتحقق من الإلغاء
function isCancellationRequestedSafe() {
    try {
        return typeof isCancellationRequested !== 'undefined' && isCancellationRequested === true;
    } catch (error) {
        return false;
    }
}

// دالة بسيطة للتحقق من المكتبات
function checkRequiredLibraries() {
    if (typeof ExcelJS === 'undefined') {
        console.error('ExcelJS not found');
        throw new Error('ExcelJS library is required');
    }

    if (typeof saveAs === 'undefined') {
        console.error('saveAs not found');
        throw new Error('FileSaver library is required');
    }

    return true;
}

// دالة بسيطة لإنشاء Excel
async function createExcelFileFast(invoiceData) {
    try {
        console.log(`Creating Excel for ${invoiceData.length} invoices...`);

        // التحقق من المكتبات
        checkRequiredLibraries();

        // إنشاء workbook
        const workbook = new ExcelJS.Workbook();
        workbook.creator = 'Oditlz ETA Tool';

        const worksheet = workbook.addWorksheet('جميع الفواتير');

        // تعريف الأعمدة
        worksheet.columns = [
            { header: 'الرقم', key: 'index', width: 10 },
            { header: 'رقم الفاتورة', key: 'invoiceNumber', width: 20 },
            { header: 'تاريخ الإصدار', key: 'dateTimeIssued', width: 20 },
            { header: 'نوع الفاتورة', key: 'invoiceType', width: 15 },
            { header: 'العملة', key: 'currency', width: 10 },
            { header: 'إجمالي المبلغ', key: 'totalAmount', width: 15 },
            { header: 'الضريبة', key: 'totalTaxAmount', width: 15 },
            { header: 'المبلغ النهائي', key: 'netAmount', width: 15 },
            { header: 'الحالة', key: 'status', width: 15 },
            { header: 'UUID', key: 'uuid', width: 40 }
        ];

        // إضافة البيانات
        invoiceData.forEach((invoice, index) => {
            worksheet.addRow({
                index: index + 1,
                invoiceNumber: invoice.invoiceNumber || '',
                dateTimeIssued: invoice.dateTimeIssued || '',
                invoiceType: invoice.invoiceType || '',
                currency: invoice.currency || '',
                totalAmount: invoice.totalAmount || 0,
                totalTaxAmount: invoice.totalTaxAmount || 0,
                netAmount: invoice.netAmount || 0,
                status: invoice.status || '',
                uuid: invoice.uuid || ''
            });
        });

        // إنشاء وحفظ الملف
        const buffer = await workbook.xlsx.writeBuffer();
        const blob = new Blob([buffer], {
            type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        });

        saveAs(blob, "eInvoices_Fast.xlsx");
        console.log('Excel file created successfully');

    } catch (error) {
        console.error('Error creating Excel:', error);
        throw error;
    }
}

// دالة بسيطة لإنشاء Excel للإيصالات
async function createReceiptExcelFast(receiptData) {
    try {
        console.log(`Creating Excel for ${receiptData.length} receipts...`);

        // التحقق من المكتبات
        checkRequiredLibraries();

        // إنشاء workbook
        const workbook = new ExcelJS.Workbook();
        workbook.creator = 'Oditlz ETA Tool';

        const worksheet = workbook.addWorksheet('جميع الإيصالات');

        // تعريف الأعمدة
        worksheet.columns = [
            { header: 'الرقم', key: 'index', width: 10 },
            { header: 'رقم الإيصال', key: 'receiptNumber', width: 20 },
            { header: 'تاريخ الإصدار', key: 'dateTimeIssued', width: 20 },
            { header: 'نوع الإيصال', key: 'receiptType', width: 15 },
            { header: 'العملة', key: 'currency', width: 10 },
            { header: 'إجمالي المبلغ', key: 'totalAmount', width: 15 },
            { header: 'الحالة', key: 'status', width: 15 },
            { header: 'UUID', key: 'uuid', width: 40 }
        ];

        // إضافة البيانات
        receiptData.forEach((receipt, index) => {
            worksheet.addRow({
                index: index + 1,
                receiptNumber: receipt.receiptNumber || '',
                dateTimeIssued: receipt.dateTimeIssued || '',
                receiptType: receipt.receiptType || '',
                currency: receipt.currency || '',
                totalAmount: receipt.totalAmount || 0,
                status: receipt.status || '',
                uuid: receipt.uuid || ''
            });
        });

        // إنشاء وحفظ الملف
        const buffer = await workbook.xlsx.writeBuffer();
        const blob = new Blob([buffer], {
            type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        });

        saveAs(blob, "eReceipts_Fast.xlsx");
        console.log('Receipt Excel file created successfully');

    } catch (error) {
        console.error('Error creating Receipt Excel:', error);
        throw error;
    }
}

// دالة بسيطة لتحميل الفواتير
async function downloadExcelSummaryFast(paramList) {
    try {
        if (!paramList || !Array.isArray(paramList) || paramList.length === 0) {
            console.log('No items to process');
            return;
        }

        $('#downloadModal').modal('hide');
        $(".downloadAllBtn").addClass("disabled");
        $(".downloadAllBtnText").html("Processing...");

        console.log(`Processing ${paramList.length} invoices...`);

        // تحميل البيانات
        const results = [];
        for (let i = 0; i < paramList.length; i++) {
            if (isCancellationRequestedSafe()) {
                console.log('Processing cancelled');
                break;
            }

            try {
                const response = await fetch(paramList[i]);
                if (response.ok) {
                    const data = await response.json();
                    results.push(data);
                }
            } catch (error) {
                console.warn(`Failed to load item ${i + 1}:`, error);
            }

            // تحديث التقدم
            const progress = Math.round(((i + 1) / paramList.length) * 100);
            $(".downloadAllBtnText").html(`Processing... ${progress}%`);
        }

        if (results.length > 0) {
            $(".downloadAllBtnText").html("Creating Excel...");
            await createExcelFileFast(results);
            $(".downloadAllBtnText").html("Complete!");
        } else {
            $(".downloadAllBtnText").html("No data found");
        }

        // إعادة تعيين الواجهة
        setTimeout(() => {
            $(".downloadAllBtn").removeClass("disabled");
            $(".downloadAllBtnText").html("Download All");
        }, 2000);

    } catch (error) {
        console.error('Error in downloadExcelSummaryFast:', error);
        $(".downloadAllBtnText").html("Error occurred");

        setTimeout(() => {
            $(".downloadAllBtn").removeClass("disabled");
            $(".downloadAllBtnText").html("Download All");
        }, 3000);
    }
}

// نهاية الملف - Fast Excel Functions v1.0.0
console.log('Fast Excel Functions loaded successfully');