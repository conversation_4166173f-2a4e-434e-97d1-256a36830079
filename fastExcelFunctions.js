/**
 * Fast Excel Functions - Optimized Performance
 * <AUTHOR> Team <<EMAIL>>
 * @license  MIT
 * @version  1.1.0
 * 
 * High-performance Excel generation functions with parallel processing
 */

// دالة محسنة وسريعة لتحميل Excel للفواتير
async function downloadExcelSummaryFast(paramList) {
    try {
        $('#downloadModal').modal('hide');
        const totalCount = paramList.length;
        
        if (totalCount === 0) return;
        
        const btn = $(".downloadAllBtn");
        $(btn).addClass("disabled");
        $("#cancelDownloadBtn").show();
        
        // تحسين: معالجة متوازية مع حد أقصى للطلبات المتزامنة
        const MAX_CONCURRENT = 3; // حد أقصى 3 طلبات متزامنة
        const results = [];
        
        for (let i = 0; i < paramList.length; i += MAX_CONCURRENT) {
            if (isCancellationRequested) break;
            
            const batch = paramList.slice(i, i + MAX_CONCURRENT);
            $(".downloadAllBtnText").html(`Loading ${Math.min(i + MAX_CONCURRENT, totalCount)}/${totalCount}`);
            
            const batchPromises = batch.map(async (param) => {
                try {
                    const e = param.source || param;
                    return await getDocumentDetails(e.uuid);
                } catch (error) {
                    console.warn('Failed to load invoice:', error);
                    return null;
                }
            });

            const batchResults = await Promise.all(batchPromises);
            results.push(...batchResults.filter(r => r !== null));
        }

        if (!isCancellationRequested && results.length > 0) {
            $(".downloadAllBtnText").html("Creating Excel...");
            await createExcelFileFast(results);
        }
        
    } catch (error) {
        console.error('Error in downloadExcelSummaryFast:', error);
    } finally {
        $(".downloadAllBtnText").html("Download All");
        $(".downloadAllBtn").removeClass("disabled");
        $("#cancelDownloadBtn").hide();
        isCancellationRequested = false;
    }
}

// دالة سريعة لإنشاء Excel
async function createExcelFileFast(invoiceData) {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet("جميع الفواتير");
    
    worksheet.views = [{ rightToLeft: true, rtl: true, activeCell: 'A1', state: "frozen", ySplit: 1 }];
    
    // تحديد الأعمدة مسبقاً
    const columns = [
        { header: 'مسلسل', key: 'index', width: 10 },
        { header: 'نوع المستند', key: 'type', width: 12 },
        { header: 'الحالة', key: 'status', width: 10 },
        { header: 'تاريخ الإصدار', key: 'dateIssued', width: 13 },
        { header: 'قيمة الفاتورة', key: 'netAmount', width: 12 },
        { header: 'إجمالى الفاتورة', key: 'totalAmount', width: 14 },
        { header: 'الرقم الداخلى', key: 'invoiceNumber', width: 23 },
        { header: 'إسم البائع', key: 'issuerName', width: 32 },
        { header: 'إسم المشترى', key: 'receiverName', width: 32 },
        { header: 'التوقيع الرقمي', key: 'digitalSignature', width: 25 },
        { header: 'حالة التوقيع', key: 'signatureStatus', width: 15 }
    ];
    
    worksheet.columns = columns;
    
    // معالجة البيانات بشكل مجمع
    const rows = invoiceData.map((doc, index) => ({
        index: index + 1,
        type: doc.documentTypeNameAr || doc.documentTypeNameSecondaryLang || '',
        status: doc.status || '',
        dateIssued: doc.issueDate || doc.dateTimeIssued || '',
        netAmount: doc.netAmount || 0,
        totalAmount: doc.totalAmount || 0,
        invoiceNumber: doc.internalId || '',
        issuerName: doc.issuerName || '',
        receiverName: doc.receiverName || '',
        digitalSignature: doc.signatures && doc.signatures.length > 0 ? 'موقع رقمياً' : 'غير موقع',
        signatureStatus: doc.signatures && doc.signatures.length > 0 ? 'صالح' : 'غير متوفر'
    }));
    
    // إضافة جميع الصفوف دفعة واحدة
    worksheet.addRows(rows);
    
    // إنشاء وحفظ الملف
    const buffer = await workbook.xlsx.writeBuffer();
    const blob = new Blob([buffer], { type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" });
    saveAs(blob, "eInvoices_Fast.xlsx");
}

// دالة محسنة وسريعة لتحميل Excel للإيصالات
async function downloadExcelForReceiptsFast(paramList) {
    try {
        $('#downloadModal').modal('hide');
        const totalCount = paramList.length;
        
        if (totalCount === 0) return;
        
        const btn = $(".downloadAllBtn");
        $(btn).addClass("disabled");
        $("#cancelDownloadBtn").show();
        
        // تحسين: معالجة متوازية مع حد أقصى للطلبات المتزامنة
        const MAX_CONCURRENT = 3;
        const results = [];
        
        for (let i = 0; i < paramList.length; i += MAX_CONCURRENT) {
            if (isCancellationRequested) break;
            
            const batch = paramList.slice(i, i + MAX_CONCURRENT);
            $(".downloadAllBtnText").html(`Loading ${Math.min(i + MAX_CONCURRENT, totalCount)}/${totalCount}`);
            
            const batchPromises = batch.map(async (param) => {
                try {
                    const e = param.source || param;
                    return await getDocumentDetails(e.uuid);
                } catch (error) {
                    console.warn('Failed to load receipt:', error);
                    return null;
                }
            });

            const batchResults = await Promise.all(batchPromises);
            results.push(...batchResults.filter(r => r !== null));
        }

        if (!isCancellationRequested && results.length > 0) {
            $(".downloadAllBtnText").html("Creating Excel...");
            await createReceiptExcelFast(results);
        }
        
    } catch (error) {
        console.error('Error in downloadExcelForReceiptsFast:', error);
    } finally {
        $(".downloadAllBtnText").html("Download All");
        $(".downloadAllBtn").removeClass("disabled");
        $("#cancelDownloadBtn").hide();
        isCancellationRequested = false;
    }
}

// دالة سريعة لإنشاء Excel للإيصالات
async function createReceiptExcelFast(receiptData) {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet("جميع الإيصالات");
    
    worksheet.views = [{ rightToLeft: true, rtl: true, activeCell: 'A1', state: "frozen", ySplit: 1 }];
    
    // تحديد الأعمدة مسبقاً
    const columns = [
        { header: 'مسلسل', key: 'index', width: 10 },
        { header: 'نوع المستند', key: 'type', width: 12 },
        { header: 'الحالة', key: 'status', width: 10 },
        { header: 'تاريخ الإصدار', key: 'dateIssued', width: 13 },
        { header: 'قيمة الإيصال', key: 'netAmount', width: 12 },
        { header: 'إجمالى الإيصال', key: 'totalAmount', width: 14 },
        { header: 'الرقم الداخلى', key: 'receiptNumber', width: 23 },
        { header: 'إسم البائع', key: 'issuerName', width: 32 },
        { header: 'إسم المشترى', key: 'receiverName', width: 32 },
        { header: 'التوقيع الرقمي', key: 'digitalSignature', width: 25 },
        { header: 'حالة التوقيع', key: 'signatureStatus', width: 15 }
    ];
    
    worksheet.columns = columns;
    
    // معالجة البيانات بشكل مجمع
    const rows = receiptData.map((doc, index) => ({
        index: index + 1,
        type: doc.documentTypeNameAr || doc.documentTypeNameSecondaryLang || '',
        status: doc.status || '',
        dateIssued: doc.issueDate || doc.dateTimeIssued || '',
        netAmount: doc.netAmount || 0,
        totalAmount: doc.totalAmount || 0,
        receiptNumber: doc.internalId || '',
        issuerName: doc.issuerName || '',
        receiverName: doc.receiverName || '',
        digitalSignature: doc.signatures && doc.signatures.length > 0 ? 'موقع رقمياً' : 'غير موقع',
        signatureStatus: doc.signatures && doc.signatures.length > 0 ? 'صالح' : 'غير متوفر'
    }));
    
    // إضافة جميع الصفوف دفعة واحدة
    worksheet.addRows(rows);
    
    // إنشاء وحفظ الملف
    const buffer = await workbook.xlsx.writeBuffer();
    const blob = new Blob([buffer], { type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" });
    saveAs(blob, "eReceipts_Fast.xlsx");
}

// دالة مساعدة لتحسين الأداء - تحميل متوازي محدود
async function loadDocumentsBatch(paramList, batchSize = 3) {
    const results = [];
    
    for (let i = 0; i < paramList.length; i += batchSize) {
        const batch = paramList.slice(i, i + batchSize);
        
        const batchPromises = batch.map(async (param) => {
            try {
                const e = param.source || param;
                return await getDocumentDetails(e.uuid);
            } catch (error) {
                console.warn('Failed to load document:', error);
                return null;
            }
        });

        const batchResults = await Promise.all(batchPromises);
        results.push(...batchResults.filter(r => r !== null));
        
        // تحديث مؤشر التقدم
        const progress = Math.min(i + batchSize, paramList.length);
        $(".downloadAllBtnText").html(`Loading ${progress}/${paramList.length}`);
    }
    
    return results;
}

// دالة مساعدة لإنشاء Excel بسرعة
async function generateExcelFast(data, worksheetName, columns, filename) {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet(worksheetName);
    
    worksheet.views = [{ rightToLeft: true, rtl: true, activeCell: 'A1', state: "frozen", ySplit: 1 }];
    worksheet.columns = columns;
    worksheet.addRows(data);
    
    const buffer = await workbook.xlsx.writeBuffer();
    const blob = new Blob([buffer], { type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" });
    saveAs(blob, filename);
}
