/**
 * Fast Excel Functions - Optimized Performance
 * <AUTHOR> Team <<EMAIL>>
 * @license  MIT
 * @version  1.1.0
 *
 * High-performance Excel generation functions with parallel processing
 */

// فحص أولي للمكتبات عند تحميل الملف
(function() {
    console.log('Fast Excel Functions loading...');

    // إنشاء الدوال المحلية فوراً كاحتياط
    try {
        if (typeof ExcelJS === 'undefined') {
            console.log('Creating local ExcelJS immediately...');
            createLocalExcelJS();
        }

        if (typeof saveAs === 'undefined') {
            console.log('Creating local saveAs immediately...');
            createLocalSaveAs();
        }
    } catch (immediateError) {
        console.warn('Error creating immediate fallbacks:', immediateError);
    }

    // تأخير قصير للسماح للمكتبات بالتحميل
    setTimeout(() => {
        try {
            if (typeof ExcelJS === 'undefined') {
                console.warn('ExcelJS not detected, using local fallback');
                createLocalExcelJS();
            } else {
                console.log('ExcelJS detected and ready');
            }

            if (typeof saveAs === 'undefined') {
                console.warn('saveAs not detected, using local fallback');
                createLocalSaveAs();
            } else {
                console.log('saveAs detected and ready');
            }

            if (typeof $ === 'undefined') {
                console.warn('jQuery not detected');
            } else {
                console.log('jQuery detected and ready');
            }
        } catch (error) {
            console.warn('Error during initial library check:', error);
            // إنشاء الدوال كحل أخير
            try {
                createLocalExcelJS();
                createLocalSaveAs();
                console.log('Local fallback functions created as last resort');
            } catch (lastResortError) {
                console.error('Failed to create local fallback functions:', lastResortError);
            }
        }
    }, 1000);
})();

// دالة مساعدة للتحقق من حالة الإلغاء
function isCancellationRequestedSafe() {
    try {
        return typeof isCancellationRequested !== 'undefined' && isCancellationRequested === true;
    } catch (error) {
        return false;
    }
}

// دالة مساعدة لتعيين حالة الإلغاء
function setCancellationRequested(value) {
    try {
        if (typeof isCancellationRequested !== 'undefined') {
            isCancellationRequested = value;
        }
    } catch (error) {
        console.warn('Could not set cancellation status:', error);
    }
}

// دالة مساعدة لمعالجة الأخطاء بشكل آمن
function safeErrorHandler(error, functionName) {
    let errorMessage = 'Unknown error';
    let errorStack = null;
    let errorName = 'Error';

    try {
        if (error) {
            if (typeof error === 'string') {
                errorMessage = error;
            } else if (error.message) {
                errorMessage = error.message;
            } else if (error.toString) {
                errorMessage = error.toString();
            } else {
                errorMessage = JSON.stringify(error);
            }

            errorStack = error.stack || null;
            errorName = error.name || 'Error';
        }
    } catch (parseError) {
        errorMessage = 'Error parsing error object';
        console.warn('Failed to parse error in', functionName, ':', parseError);
    }

    return {
        message: errorMessage,
        stack: errorStack,
        name: errorName,
        originalError: error
    };
}

// دالة مساعدة لتحديد رسالة المستخدم من الخطأ
function getUserMessageFromError(errorMessage) {
    let userMessage = "Error occurred - Try again";

    try {
        if (errorMessage) {
            const lowerMessage = errorMessage.toLowerCase();

            if (lowerMessage.includes('network') || lowerMessage.includes('connection')) {
                userMessage = "Network error - Check connection";
            } else if (lowerMessage.includes('timeout')) {
                userMessage = "Request timeout - Try again";
            } else if (lowerMessage.includes('authentication') || lowerMessage.includes('unauthorized')) {
                userMessage = "Session expired - Please refresh";
            } else if (lowerMessage.includes('excel') || lowerMessage.includes('workbook')) {
                userMessage = "Excel generation failed - Try again";
            } else if (lowerMessage.includes('cancelled') || lowerMessage.includes('abort')) {
                userMessage = "Operation cancelled";
            } else if (lowerMessage.includes('not found') || lowerMessage.includes('404')) {
                userMessage = "Document not found";
            } else if (lowerMessage.includes('forbidden') || lowerMessage.includes('403')) {
                userMessage = "Access denied";
            } else if (lowerMessage.includes('server') || lowerMessage.includes('500')) {
                userMessage = "Server error - Try again later";
            }
        }
    } catch (messageError) {
        console.warn('Error determining user message:', messageError);
        userMessage = "Error occurred - Try again";
    }

    return userMessage;
}

// دالة للتحقق من توفر المكتبات المطلوبة مع إنشاء احتياطي
function checkRequiredLibraries() {
    const missingLibraries = [];
    let hasErrors = false;

    // فحص ExcelJS
    if (typeof ExcelJS === 'undefined') {
        console.warn('ExcelJS not found, attempting to create fallback...');
        try {
            // محاولة إنشاء ExcelJS احتياطي
            if (typeof window !== 'undefined' && typeof window.createEnhancedExcelJS === 'function') {
                window.createEnhancedExcelJS();
                console.log('ExcelJS fallback created successfully');
            } else if (typeof createLocalExcelJS === 'function') {
                createLocalExcelJS();
                console.log('Local ExcelJS created successfully');
            } else {
                missingLibraries.push('ExcelJS');
                hasErrors = true;
            }
        } catch (error) {
            console.error('Failed to create ExcelJS fallback:', error);
            try {
                createLocalExcelJS();
                console.log('Local ExcelJS created as last resort');
            } catch (localError) {
                console.error('Local ExcelJS creation also failed:', localError);
                missingLibraries.push('ExcelJS');
                hasErrors = true;
            }
        }
    } else {
        console.log('ExcelJS is available');
    }

    // فحص saveAs
    if (typeof saveAs === 'undefined') {
        console.warn('saveAs not found, attempting to create fallback...');
        try {
            // محاولة إنشاء saveAs احتياطي
            if (typeof window !== 'undefined' && typeof window.createSaveAsFunction === 'function') {
                window.createSaveAsFunction();
                console.log('saveAs fallback created successfully');
            } else if (typeof createLocalSaveAs === 'function') {
                createLocalSaveAs();
                console.log('Local saveAs created successfully');
            } else {
                missingLibraries.push('FileSaver (saveAs)');
                hasErrors = true;
            }
        } catch (error) {
            console.error('Failed to create saveAs fallback:', error);
            try {
                createLocalSaveAs();
                console.log('Local saveAs created as last resort');
            } catch (localError) {
                console.error('Local saveAs creation also failed:', localError);
                missingLibraries.push('FileSaver (saveAs)');
                hasErrors = true;
            }
        }
    } else {
        console.log('saveAs is available');
    }

    // فحص jQuery
    if (typeof $ === 'undefined') {
        console.warn('jQuery not found');
        missingLibraries.push('jQuery');
        hasErrors = true;
    } else {
        console.log('jQuery is available');
    }

    if (hasErrors && missingLibraries.length > 0) {
        const errorMessage = `Missing required libraries: ${missingLibraries.join(', ')}`;
        console.error(errorMessage);

        // إظهار رسالة للمستخدم
        if (typeof $ !== 'undefined') {
            $(".downloadAllBtnText").html("Libraries missing - Refresh page");
            setTimeout(() => {
                $(".downloadAllBtnText").html("Download All");
            }, 5000);
        }

        throw new Error(errorMessage);
    }

    console.log('All required libraries are available');
    return true;
}

// دالة لتحميل المكتبات الاحتياطية
async function loadFallbackLibraries() {
    console.log('Loading fallback libraries...');

    try {
        // محاولة تحميل ExcelJS من CDN
        if (typeof ExcelJS === 'undefined') {
            console.log('Attempting to load ExcelJS from CDN...');

            const script = document.createElement('script');
            script.src = 'https://cdnjs.cloudflare.com/ajax/libs/exceljs/4.3.0/exceljs.min.js';
            script.crossOrigin = 'anonymous';

            const loadPromise = new Promise((resolve, reject) => {
                script.onload = resolve;
                script.onerror = reject;
                setTimeout(reject, 10000); // timeout بعد 10 ثواني
            });

            document.head.appendChild(script);

            try {
                await loadPromise;
                console.log('ExcelJS loaded from CDN successfully');
            } catch (error) {
                console.warn('Failed to load ExcelJS from CDN:', error);
                // إنشاء ExcelJS احتياطي
                if (typeof window.createEnhancedExcelJS === 'function') {
                    window.createEnhancedExcelJS();
                }
            }
        }

        // محاولة تحميل FileSaver من CDN
        if (typeof saveAs === 'undefined') {
            console.log('Attempting to load FileSaver from CDN...');

            const script = document.createElement('script');
            script.src = 'https://cdnjs.cloudflare.com/ajax/libs/FileSaver.js/2.0.5/FileSaver.min.js';
            script.crossOrigin = 'anonymous';

            const loadPromise = new Promise((resolve, reject) => {
                script.onload = resolve;
                script.onerror = reject;
                setTimeout(reject, 10000); // timeout بعد 10 ثواني
            });

            document.head.appendChild(script);

            try {
                await loadPromise;
                console.log('FileSaver loaded from CDN successfully');
            } catch (error) {
                console.warn('Failed to load FileSaver from CDN:', error);
                // إنشاء saveAs احتياطي
                if (typeof window.createSaveAsFunction === 'function') {
                    window.createSaveAsFunction();
                }
            }
        }

        console.log('Fallback libraries loading completed');

    } catch (error) {
        console.error('Error loading fallback libraries:', error);

        // إنشاء الدوال الاحتياطية كحل أخير
        try {
            if (typeof window.createEnhancedExcelJS === 'function') {
                window.createEnhancedExcelJS();
            } else {
                // إنشاء ExcelJS مباشرة
                createLocalExcelJS();
            }
        } catch (excelError) {
            console.warn('Failed to create ExcelJS fallback:', excelError);
            createLocalExcelJS();
        }

        try {
            if (typeof window.createSaveAsFunction === 'function') {
                window.createSaveAsFunction();
            } else {
                // إنشاء saveAs مباشرة
                createLocalSaveAs();
            }
        } catch (saveError) {
            console.warn('Failed to create saveAs fallback:', saveError);
            createLocalSaveAs();
        }
    }
}

// دالة محلية لإنشاء ExcelJS
function createLocalExcelJS() {
    if (typeof ExcelJS === 'undefined') {
        window.ExcelJS = {
            Workbook: function() {
                this.creator = 'Oditlz ETA Tool';
                this.lastModifiedBy = 'Oditlz Team';
                this.created = new Date();
                this.modified = new Date();
                this.worksheets = [];

                this.addWorksheet = function(name) {
                    const worksheet = {
                        name: name || 'Sheet1',
                        columns: [],
                        rows: [],
                        _cells: {},
                        views: [],
                        autoFilter: null,

                        set columns(cols) {
                            this._columns = cols || [];
                        },

                        get columns() {
                            return this._columns || [];
                        },

                        addRows: function(data) {
                            if (Array.isArray(data)) {
                                this.rows = this.rows.concat(data);
                            }
                        },

                        addRow: function(data) {
                            if (data) {
                                this.rows.push(data);
                            }
                        },

                        getCell: function(address) {
                            if (!this._cells[address]) {
                                this._cells[address] = {
                                    font: {},
                                    fill: {},
                                    alignment: {},
                                    value: null
                                };
                            }
                            return this._cells[address];
                        },

                        getRow: function(rowNumber) {
                            return {
                                font: {},
                                fill: {},
                                alignment: {},
                                values: []
                            };
                        },

                        getColumn: function(colNumber) {
                            return {
                                width: 10,
                                style: {}
                            };
                        }
                    };

                    this.worksheets.push(worksheet);
                    return worksheet;
                };

                this.xlsx = {
                    writeBuffer: async function(options = {}) {
                        try {
                            console.log('Generating Excel buffer with local ExcelJS...');

                            const workbook = this.parent || window.currentWorkbook;
                            if (!workbook || !workbook.worksheets || workbook.worksheets.length === 0) {
                                throw new Error('No worksheets to export');
                            }

                            // إنشاء محتوى CSV كبديل
                            let csvContent = '\uFEFF'; // BOM for UTF-8
                            const worksheet = workbook.worksheets[0];

                            if (worksheet.columns && worksheet.columns.length > 0) {
                                const headers = worksheet.columns.map(col => col.header || '').join(',');
                                csvContent += headers + '\n';
                            }

                            if (worksheet.rows && worksheet.rows.length > 0) {
                                worksheet.rows.forEach(row => {
                                    if (typeof row === 'object' && row !== null) {
                                        const values = worksheet.columns.map(col => {
                                            const value = row[col.key] || '';
                                            return typeof value === 'string' ?
                                                `"${value.replace(/"/g, '""')}"` :
                                                value;
                                        }).join(',');
                                        csvContent += values + '\n';
                                    }
                                });
                            }

                            const encoder = new TextEncoder();
                            const buffer = encoder.encode(csvContent);

                            console.log(`Local Excel buffer generated: ${buffer.length} bytes`);
                            return buffer.buffer;

                        } catch (error) {
                            console.error('Error generating local Excel buffer:', error);
                            return new ArrayBuffer(8);
                        }
                    }
                };

                this.xlsx.parent = this;
                window.currentWorkbook = this;
                return this;
            }
        };
        console.log('Local ExcelJS created successfully');
    }
}

// دالة محلية لإنشاء saveAs
function createLocalSaveAs() {
    if (typeof saveAs === 'undefined') {
        window.saveAs = function(blob, filename) {
            try {
                console.log(`Saving file locally: ${filename}`);

                const url = window.URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.href = url;
                link.download = filename || 'download.csv';
                link.style.display = 'none';

                document.body.appendChild(link);
                link.click();

                setTimeout(() => {
                    document.body.removeChild(link);
                    window.URL.revokeObjectURL(url);
                }, 100);

                console.log(`File saved successfully: ${filename}`);

            } catch (error) {
                console.error('Error saving file locally:', error);

                try {
                    const reader = new FileReader();
                    reader.onload = function() {
                        const dataUrl = reader.result;
                        const link = document.createElement('a');
                        link.href = dataUrl;
                        link.download = filename || 'download.csv';
                        document.body.appendChild(link);
                        link.click();
                        document.body.removeChild(link);
                    };
                    reader.readAsDataURL(blob);
                } catch (fallbackError) {
                    console.error('Fallback save method also failed:', fallbackError);
                    if (typeof $ !== 'undefined') {
                        $(".downloadAllBtnText").html("Save failed - Try again");
                        setTimeout(() => {
                            $(".downloadAllBtnText").html("Download All");
                        }, 3000);
                    }
                }
            }
        };
        console.log('Local saveAs function created successfully');
    }
}

// دالة لمعالجة أخطاء إنشاء Excel بشكل خاص
function handleExcelGenerationError(error, context = {}) {
    const errorDetails = safeErrorHandler(error, 'Excel Generation');

    let userMessage = "Excel generation failed - Try again";
    const lowerMessage = errorDetails.message.toLowerCase();

    if (lowerMessage.includes('exceljs') || lowerMessage.includes('workbook')) {
        userMessage = "Excel library error - Please refresh page";
    } else if (lowerMessage.includes('memory') || lowerMessage.includes('heap')) {
        userMessage = "Too much data - Try smaller selection";
    } else if (lowerMessage.includes('saveas') || lowerMessage.includes('download')) {
        userMessage = "Download failed - Check browser settings";
    } else if (lowerMessage.includes('invalid') || lowerMessage.includes('empty')) {
        userMessage = "Invalid data - No items to export";
    }

    return {
        ...errorDetails,
        userMessage,
        context
    };
}

// دالة محسنة وسريعة لتحميل Excel للفواتير
async function downloadExcelSummaryFast(paramList) {
    try {
        // التحقق من صحة المدخلات
        if (!paramList || !Array.isArray(paramList)) {
            console.error('Invalid paramList provided to downloadExcelSummaryFast');
            return;
        }

        $('#downloadModal').modal('hide');
        const totalCount = paramList.length;

        if (totalCount === 0) {
            console.log('No items to process in downloadExcelSummaryFast');
            return;
        }
        
        const btn = $(".downloadAllBtn");
        $(btn).addClass("disabled");
        $("#cancelDownloadBtn").show();
        
        // تحسين متقدم: معالجة متوازية ذكية مع تكيف ديناميكي
        const MAX_CONCURRENT = Math.min(10, Math.max(5, Math.ceil(totalCount / 10))); // 5-10 طلبات حسب العدد
        const CHUNK_SIZE = Math.min(50, Math.max(10, Math.ceil(totalCount / 5))); // حجم القطعة الديناميكي

        console.log(`Optimized settings: ${MAX_CONCURRENT} concurrent, ${CHUNK_SIZE} chunk size`);

        const results = [];
        let processedCount = 0;

        // معالجة بالقطع الكبيرة مع طلبات متوازية
        for (let chunkStart = 0; chunkStart < paramList.length; chunkStart += CHUNK_SIZE) {
            if (isCancellationRequestedSafe()) {
                console.log('Processing cancelled by user');
                break;
            }

            const chunk = paramList.slice(chunkStart, chunkStart + CHUNK_SIZE);
            const chunkResults = [];

            try {

            // معالجة القطعة بطلبات متوازية
            for (let i = 0; i < chunk.length; i += MAX_CONCURRENT) {
                if (isCancellationRequestedSafe()) break;

                const batch = chunk.slice(i, i + MAX_CONCURRENT);
                processedCount += batch.length;

                $(".downloadAllBtnText").html(`Loading ${processedCount}/${totalCount} (${Math.round(processedCount/totalCount*100)}%)`);

                const batchPromises = batch.map(async (param) => {
                    try {
                        const e = param.source || param;

                        // التحقق من صحة UUID
                        if (!e.uuid || typeof e.uuid !== 'string' || e.uuid.length < 10) {
                            console.warn(`Invalid UUID: ${e.uuid}`);
                            return null;
                        }

                        // تخطي UUIDs المعروفة بالمشاكل
                        const problematicUUIDs = ['21b59e86-9553-45c8-bc48-c0b5e4801d1c'];
                        if (problematicUUIDs.includes(e.uuid)) {
                            console.warn(`Skipping problematic UUID: ${e.uuid}`);
                            return null;
                        }

                        const startTime = performance.now();
                        const result = await getDocumentDetails(e.uuid);
                        const endTime = performance.now();

                        // تسجيل الأداء للمراقبة
                        if (endTime - startTime > 5000) {
                            console.warn(`Slow request: ${e.uuid} took ${Math.round(endTime - startTime)}ms`);
                        }

                        return result;
                    } catch (error) {
                        console.warn('Failed to load invoice:', (param.source || param).uuid || param.id, error.message || error);
                        return null;
                    }
                });

                const batchResults = await Promise.all(batchPromises);
                const validResults = batchResults.filter(r => r !== null && r !== undefined);
                chunkResults.push(...validResults);

                // تأخير قصير لتجنب إرهاق الخادم
                if (i + MAX_CONCURRENT < chunk.length) {
                    await new Promise(resolve => setTimeout(resolve, 100));
                }
            }

            // إضافة النتائج الصحيحة فقط
            if (chunkResults && chunkResults.length > 0) {
                const validChunkResults = chunkResults.filter(r => r !== null && r !== undefined);
                if (validChunkResults.length > 0) {
                    results.push(...validChunkResults);
                }
            }

            // تحديث التقدم
            const progress = Math.round((chunkStart + CHUNK_SIZE) / paramList.length * 100);
            console.log(`Chunk completed: ${chunkResults.length}/${chunk.length} items, Progress: ${progress}%`);

            } catch (chunkError) {
                console.error(`Error processing chunk ${chunkStart}-${chunkStart + CHUNK_SIZE}:`, chunkError);
                // المتابعة مع القطعة التالية
            }
        }

        if (!isCancellationRequestedSafe() && results.length > 0) {
            $(".downloadAllBtnText").html("Creating Excel...");

            // تحسين تلقائي حسب حجم البيانات
            if (results.length > 500) {
                console.log(`Large dataset detected (${results.length} items), using optimized processing...`);
                await createExcelFileUltraFast(results);
            } else {
                await createExcelFileFast(results);
            }
        }
        
    } catch (error) {
        // استخدام الدالة المساعدة لمعالجة الأخطاء
        const errorDetails = safeErrorHandler(error, 'downloadExcelSummaryFast');

        const errorInfo = {
            ...errorDetails,
            totalCount: paramList ? paramList.length : 'unknown',
            timestamp: new Date().toISOString(),
            function: 'downloadExcelSummaryFast'
        };

        console.error('Error in downloadExcelSummaryFast:', errorInfo);

        // تحديد رسالة المستخدم
        const userMessage = getUserMessageFromError(errorDetails.message);

        // إظهار رسالة خطأ للمستخدم
        $(".downloadAllBtnText").html(userMessage);

        // محاولة إعادة تعيين الواجهة بعد تأخير قصير
        setTimeout(() => {
            $(".downloadAllBtnText").html("Download All");
        }, 5000); // زيادة الوقت لقراءة الرسالة

    } finally {
        $(".downloadAllBtn").removeClass("disabled");
        $("#cancelDownloadBtn").hide();
        setCancellationRequested(false);
    }
}

// دالة سريعة لإنشاء Excel
async function createExcelFileFast(invoiceData) {
    try {
        console.log(`Creating Excel for ${invoiceData.length} invoices...`);

        // التحقق من توفر المكتبات المطلوبة مع تحميل احتياطي
        try {
            checkRequiredLibraries();
        } catch (libraryError) {
            console.warn('Required libraries missing, attempting fallback loading...');
            try {
                await loadFallbackLibraries();
                // إعادة المحاولة بعد التحميل الاحتياطي
                checkRequiredLibraries();
            } catch (fallbackError) {
                console.error('Fallback loading failed:', fallbackError);
                // استخدام الدوال المحلية كحل أخير
                createLocalExcelJS();
                createLocalSaveAs();
                console.log('Using local fallback functions');
            }
        }

        // التحقق من صحة البيانات
        if (!invoiceData || !Array.isArray(invoiceData) || invoiceData.length === 0) {
            throw new Error('Invalid or empty invoice data provided');
        }

        // تحديد الأعمدة مسبقاً
        const columns = [
            { header: 'مسلسل', key: 'index', width: 10 },
            { header: 'نوع المستند', key: 'type', width: 12 },
            { header: 'الحالة', key: 'status', width: 10 },
            { header: 'تاريخ الإصدار', key: 'dateIssued', width: 13 },
            { header: 'قيمة الفاتورة', key: 'netAmount', width: 12 },
            { header: 'إجمالى الفاتورة', key: 'totalAmount', width: 14 },
            { header: 'الرقم الداخلى', key: 'invoiceNumber', width: 23 },
            { header: 'إسم البائع', key: 'issuerName', width: 32 },
            { header: 'إسم المشترى', key: 'receiverName', width: 32 },
            { header: 'التوقيع الرقمي', key: 'digitalSignature', width: 25 },
            { header: 'حالة التوقيع', key: 'signatureStatus', width: 15 }
        ];

        // معالجة البيانات بشكل آمن
        const rows = invoiceData.map((doc, index) => {
            try {
                // معالجة آمنة للتاريخ
                let dateIssued = '';
                try {
                    if (doc.issueDate) {
                        dateIssued = new Date(doc.issueDate).toLocaleDateString('ar-EG');
                    } else if (doc.dateTimeIssued) {
                        dateIssued = new Date(doc.dateTimeIssued).toLocaleDateString('ar-EG');
                    }
                } catch (dateError) {
                    console.warn(`Invalid date for document ${index}:`, dateError);
                    dateIssued = doc.issueDate || doc.dateTimeIssued || '';
                }

                return {
                    index: index + 1,
                    type: doc.documentTypeNameAr || doc.documentTypeNameSecondaryLang || '',
                    status: doc.status || '',
                    dateIssued: dateIssued,
                    netAmount: parseFloat(doc.netAmount) || 0,
                    totalAmount: parseFloat(doc.totalAmount) || 0,
                    invoiceNumber: doc.internalId || doc.internalID || '',
                    issuerName: doc.issuerName || doc.issuer?.name || '',
                    receiverName: doc.receiverName || doc.receiver?.name || '',
                    digitalSignature: doc.signatures && doc.signatures.length > 0 ? 'موقع رقمياً' : 'غير موقع',
                    signatureStatus: doc.signatures && doc.signatures.length > 0 ? 'صالح' : 'غير متوفر'
                };
            } catch (rowError) {
                console.warn(`Error processing row ${index}:`, rowError);
                return {
                    index: index + 1,
                    type: 'خطأ في البيانات',
                    status: 'خطأ',
                    dateIssued: '',
                    netAmount: 0,
                    totalAmount: 0,
                    invoiceNumber: '',
                    issuerName: '',
                    receiverName: '',
                    digitalSignature: '',
                    signatureStatus: ''
                };
            }
        });

        // إنشاء وحفظ الملف باستخدام الدالة المحسنة
        await generateExcelUltraFast(rows, "جميع الفواتير", columns, "eInvoices_Fast.xlsx");

    } catch (error) {
        const errorInfo = handleExcelGenerationError(error, {
            function: 'createExcelFileFast',
            dataLength: invoiceData ? invoiceData.length : 'unknown'
        });

        console.error('Error in createExcelFileFast:', errorInfo);

        // إظهار رسالة للمستخدم
        $(".downloadAllBtnText").html(errorInfo.userMessage);
        setTimeout(() => {
            $(".downloadAllBtnText").html("Download All");
        }, 5000);

        throw new Error(errorInfo.userMessage);
    }
}

// دالة محسنة للمجموعات الكبيرة من الفواتير
async function createExcelFileUltraFast(invoiceData) {
    console.log(`Creating ultra-fast Excel for ${invoiceData.length} invoices...`);

    // تحديد الأعمدة الأساسية فقط للسرعة
    const columns = [
        { header: 'مسلسل', key: 'index', width: 8 },
        { header: 'نوع المستند', key: 'type', width: 10 },
        { header: 'الحالة', key: 'status', width: 8 },
        { header: 'تاريخ الإصدار', key: 'dateIssued', width: 12 },
        { header: 'قيمة الفاتورة', key: 'netAmount', width: 12 },
        { header: 'إجمالى الفاتورة', key: 'totalAmount', width: 12 },
        { header: 'الرقم الداخلى', key: 'invoiceNumber', width: 20 },
        { header: 'إسم البائع', key: 'issuerName', width: 25 },
        { header: 'إسم المشترى', key: 'receiverName', width: 25 },
        { header: 'التوقيع الرقمي', key: 'digitalSignature', width: 15 },
        { header: 'حالة التوقيع', key: 'signatureStatus', width: 12 }
    ];

    // معالجة البيانات بشكل مبسط وسريع
    const rows = invoiceData.map((doc, index) => {
        // استخراج البيانات الأساسية فقط
        const dateIssued = doc.issueDate || doc.dateTimeIssued || '';
        const formattedDate = dateIssued ? new Date(dateIssued).toLocaleDateString('ar-EG') : '';

        return {
            index: index + 1,
            type: (doc.documentTypeNameAr || doc.documentTypeNameSecondaryLang || '').substring(0, 15),
            status: (doc.status || '').substring(0, 10),
            dateIssued: formattedDate,
            netAmount: parseFloat(doc.netAmount) || 0,
            totalAmount: parseFloat(doc.totalAmount) || 0,
            invoiceNumber: (doc.internalId || '').substring(0, 25),
            issuerName: (doc.issuerName || '').substring(0, 30),
            receiverName: (doc.receiverName || '').substring(0, 30),
            digitalSignature: doc.signatures && doc.signatures.length > 0 ? 'موقع' : 'غير موقع',
            signatureStatus: doc.signatures && doc.signatures.length > 0 ? 'صالح' : 'غير متوفر'
        };
    });

    // إنشاء Excel مع تحسينات قصوى
    await generateExcelUltraFast(rows, "جميع الفواتير", columns, "eInvoices_UltraFast.xlsx", {
        compression: true,
        batchSize: 2000
    });
}

// دالة محسنة وسريعة لتحميل Excel للإيصالات
async function downloadExcelForReceiptsFast(paramList) {
    try {
        $('#downloadModal').modal('hide');
        const totalCount = paramList.length;
        
        if (totalCount === 0) return;
        
        const btn = $(".downloadAllBtn");
        $(btn).addClass("disabled");
        $("#cancelDownloadBtn").show();
        
        // تحسين متقدم: معالجة متوازية ذكية للإيصالات
        const MAX_CONCURRENT = Math.min(10, Math.max(5, Math.ceil(totalCount / 10)));
        const CHUNK_SIZE = Math.min(50, Math.max(10, Math.ceil(totalCount / 5)));

        console.log(`Receipts optimized settings: ${MAX_CONCURRENT} concurrent, ${CHUNK_SIZE} chunk size`);

        const results = [];
        let processedCount = 0;

        // معالجة بالقطع الكبيرة مع طلبات متوازية
        for (let chunkStart = 0; chunkStart < paramList.length; chunkStart += CHUNK_SIZE) {
            if (isCancellationRequestedSafe()) break;

            const chunk = paramList.slice(chunkStart, chunkStart + CHUNK_SIZE);
            const chunkResults = [];

            // معالجة القطعة بطلبات متوازية
            for (let i = 0; i < chunk.length; i += MAX_CONCURRENT) {
                if (isCancellationRequestedSafe()) break;

                const batch = chunk.slice(i, i + MAX_CONCURRENT);
                processedCount += batch.length;

                $(".downloadAllBtnText").html(`Loading ${processedCount}/${totalCount} (${Math.round(processedCount/totalCount*100)}%)`);

                const batchPromises = batch.map(async (param) => {
                    try {
                        const e = param.source || param;

                        // التحقق من صحة UUID
                        if (!e.uuid || typeof e.uuid !== 'string' || e.uuid.length < 10) {
                            console.warn(`Invalid receipt UUID: ${e.uuid}`);
                            return null;
                        }

                        // تخطي UUIDs المعروفة بالمشاكل
                        const problematicUUIDs = ['21b59e86-9553-45c8-bc48-c0b5e4801d1c'];
                        if (problematicUUIDs.includes(e.uuid)) {
                            console.warn(`Skipping problematic receipt UUID: ${e.uuid}`);
                            return null;
                        }

                        const startTime = performance.now();
                        const result = await getDocumentDetails(e.uuid);
                        const endTime = performance.now();

                        if (endTime - startTime > 5000) {
                            console.warn(`Slow receipt request: ${e.uuid} took ${Math.round(endTime - startTime)}ms`);
                        }

                        return result;
                    } catch (error) {
                        console.warn('Failed to load receipt:', (param.source || param).uuid || param.id, error.message || error);
                        return null;
                    }
                });

                const batchResults = await Promise.all(batchPromises);
                const validResults = batchResults.filter(r => r !== null && r !== undefined);
                chunkResults.push(...validResults);

                // تأخير قصير لتجنب إرهاق الخادم
                if (i + MAX_CONCURRENT < chunk.length) {
                    await new Promise(resolve => setTimeout(resolve, 100));
                }
            }

            // إضافة النتائج الصحيحة فقط
            if (chunkResults && chunkResults.length > 0) {
                const validChunkResults = chunkResults.filter(r => r !== null && r !== undefined);
                if (validChunkResults.length > 0) {
                    results.push(...validChunkResults);
                }
            }

            // تحديث التقدم
            const progress = Math.round((chunkStart + CHUNK_SIZE) / paramList.length * 100);
            console.log(`Receipt chunk completed: ${chunkResults.length}/${chunk.length} items, Progress: ${progress}%`);
        }

        if (!isCancellationRequestedSafe() && results.length > 0) {
            $(".downloadAllBtnText").html("Creating Excel...");
            await createReceiptExcelFast(results);
        }
        
    } catch (error) {
        // استخدام الدالة المساعدة لمعالجة الأخطاء
        const errorDetails = safeErrorHandler(error, 'downloadExcelForReceiptsFast');

        const errorInfo = {
            ...errorDetails,
            totalCount: paramList ? paramList.length : 'unknown',
            timestamp: new Date().toISOString(),
            function: 'downloadExcelForReceiptsFast'
        };

        console.error('Error in downloadExcelForReceiptsFast:', errorInfo);

        // تحديد رسالة المستخدم
        const userMessage = getUserMessageFromError(errorDetails.message);

        // إظهار رسالة خطأ للمستخدم
        $(".downloadAllBtnText").html(userMessage);

        // محاولة إعادة تعيين الواجهة بعد تأخير قصير
        setTimeout(() => {
            $(".downloadAllBtnText").html("Download All");
        }, 5000); // زيادة الوقت لقراءة الرسالة

    } finally {
        $(".downloadAllBtn").removeClass("disabled");
        $("#cancelDownloadBtn").hide();
        setCancellationRequested(false);
    }
}

// دالة سريعة لإنشاء Excel للإيصالات
async function createReceiptExcelFast(receiptData) {
    try {
        console.log(`Creating Excel for ${receiptData.length} receipts...`);

        // التحقق من توفر المكتبات المطلوبة مع تحميل احتياطي
        try {
            checkRequiredLibraries();
        } catch (libraryError) {
            console.warn('Required libraries missing, attempting fallback loading...');
            try {
                await loadFallbackLibraries();
                // إعادة المحاولة بعد التحميل الاحتياطي
                checkRequiredLibraries();
            } catch (fallbackError) {
                console.error('Fallback loading failed:', fallbackError);
                // استخدام الدوال المحلية كحل أخير
                createLocalExcelJS();
                createLocalSaveAs();
                console.log('Using local fallback functions for receipts');
            }
        }

        // التحقق من صحة البيانات
        if (!receiptData || !Array.isArray(receiptData) || receiptData.length === 0) {
            throw new Error('Invalid or empty receipt data provided');
        }

        // تحديد الأعمدة مسبقاً
        const columns = [
            { header: 'مسلسل', key: 'index', width: 10 },
            { header: 'نوع المستند', key: 'type', width: 12 },
            { header: 'الحالة', key: 'status', width: 10 },
            { header: 'تاريخ الإصدار', key: 'dateIssued', width: 13 },
            { header: 'قيمة الإيصال', key: 'netAmount', width: 12 },
            { header: 'إجمالى الإيصال', key: 'totalAmount', width: 14 },
            { header: 'الرقم الداخلى', key: 'receiptNumber', width: 23 },
            { header: 'إسم البائع', key: 'issuerName', width: 32 },
            { header: 'إسم المشترى', key: 'receiverName', width: 32 },
            { header: 'التوقيع الرقمي', key: 'digitalSignature', width: 25 },
            { header: 'حالة التوقيع', key: 'signatureStatus', width: 15 }
        ];

        // معالجة البيانات بشكل آمن
        const rows = receiptData.map((doc, index) => {
            try {
                // معالجة آمنة للتاريخ
                let dateIssued = '';
                try {
                    if (doc.issueDate) {
                        dateIssued = new Date(doc.issueDate).toLocaleDateString('ar-EG');
                    } else if (doc.dateTimeIssued) {
                        dateIssued = new Date(doc.dateTimeIssued).toLocaleDateString('ar-EG');
                    }
                } catch (dateError) {
                    console.warn(`Invalid date for receipt ${index}:`, dateError);
                    dateIssued = doc.issueDate || doc.dateTimeIssued || '';
                }

                return {
                    index: index + 1,
                    type: doc.documentTypeNameAr || doc.documentTypeNameSecondaryLang || '',
                    status: doc.status || '',
                    dateIssued: dateIssued,
                    netAmount: parseFloat(doc.netAmount) || 0,
                    totalAmount: parseFloat(doc.totalAmount) || 0,
                    receiptNumber: doc.internalId || doc.internalID || '',
                    issuerName: doc.issuerName || doc.issuer?.name || '',
                    receiverName: doc.receiverName || doc.receiver?.name || '',
                    digitalSignature: doc.signatures && doc.signatures.length > 0 ? 'موقع رقمياً' : 'غير موقع',
                    signatureStatus: doc.signatures && doc.signatures.length > 0 ? 'صالح' : 'غير متوفر'
                };
            } catch (rowError) {
                console.warn(`Error processing receipt row ${index}:`, rowError);
                return {
                    index: index + 1,
                    type: 'خطأ في البيانات',
                    status: 'خطأ',
                    dateIssued: '',
                    netAmount: 0,
                    totalAmount: 0,
                    receiptNumber: '',
                    issuerName: '',
                    receiverName: '',
                    digitalSignature: '',
                    signatureStatus: ''
                };
            }
        });

        // إنشاء وحفظ الملف باستخدام الدالة المحسنة
        await generateExcelUltraFast(rows, "جميع الإيصالات", columns, "eReceipts_Fast.xlsx");

    } catch (error) {
        console.error('Error in createReceiptExcelFast:', error);
        throw error;
    }
}

// دالة متقدمة لتحسين الأداء - تحميل متوازي ذكي
async function loadDocumentsBatchAdvanced(paramList, options = {}) {
    const {
        maxConcurrent = Math.min(15, Math.max(5, Math.ceil(paramList.length / 8))),
        chunkSize = Math.min(100, Math.max(20, Math.ceil(paramList.length / 3))),
        retryAttempts = 2,
        timeout = 10000
    } = options;

    console.log(`Advanced batch loading: ${maxConcurrent} concurrent, ${chunkSize} chunk size, ${retryAttempts} retries`);

    const results = [];
    let processedCount = 0;
    let failedCount = 0;

    // إحصائيات الأداء
    const performanceStats = {
        startTime: performance.now(),
        requestTimes: [],
        errors: []
    };

    for (let chunkStart = 0; chunkStart < paramList.length; chunkStart += chunkSize) {
        if (isCancellationRequestedSafe()) break;

        const chunk = paramList.slice(chunkStart, chunkStart + chunkSize);
        const chunkResults = [];

        // معالجة القطعة بطلبات متوازية مع إعادة المحاولة
        for (let i = 0; i < chunk.length; i += maxConcurrent) {
            if (isCancellationRequestedSafe()) break;

            const batch = chunk.slice(i, i + maxConcurrent);
            processedCount += batch.length;

            const progress = Math.round(processedCount / paramList.length * 100);
            $(".downloadAllBtnText").html(`Loading ${processedCount}/${paramList.length} (${progress}%) - Failed: ${failedCount}`);

            const batchPromises = batch.map(async (param) => {
                const e = param.source || param;

                for (let attempt = 0; attempt <= retryAttempts; attempt++) {
                    try {
                        const startTime = performance.now();

                        // إضافة timeout للطلبات
                        const timeoutPromise = new Promise((_, reject) =>
                            setTimeout(() => reject(new Error('Request timeout')), timeout)
                        );

                        const result = await Promise.race([
                            getDocumentDetails(e.uuid),
                            timeoutPromise
                        ]);

                        const endTime = performance.now();
                        const requestTime = endTime - startTime;
                        performanceStats.requestTimes.push(requestTime);

                        if (requestTime > 8000) {
                            console.warn(`Very slow request: ${e.uuid} took ${Math.round(requestTime)}ms`);
                        }

                        return result;

                    } catch (error) {
                        if (attempt === retryAttempts) {
                            failedCount++;
                            performanceStats.errors.push({
                                uuid: e.uuid,
                                error: error.message,
                                attempts: attempt + 1
                            });
                            console.warn(`Failed after ${attempt + 1} attempts:`, e.uuid, error.message);
                            return null;
                        } else {
                            console.log(`Retry ${attempt + 1} for:`, e.uuid);
                            await new Promise(resolve => setTimeout(resolve, 1000 * (attempt + 1))); // تأخير متزايد
                        }
                    }
                }
                return null;
            });

            const batchResults = await Promise.all(batchPromises);
            chunkResults.push(...batchResults.filter(r => r !== null));

            // تأخير تكيفي حسب الأداء
            const avgRequestTime = performanceStats.requestTimes.length > 0
                ? performanceStats.requestTimes.reduce((a, b) => a + b, 0) / performanceStats.requestTimes.length
                : 1000;

            const adaptiveDelay = Math.min(500, Math.max(50, avgRequestTime / 10));
            if (i + maxConcurrent < chunk.length) {
                await new Promise(resolve => setTimeout(resolve, adaptiveDelay));
            }
        }

        results.push(...chunkResults);

        // إحصائيات القطعة
        const chunkProgress = Math.round((chunkStart + chunkSize) / paramList.length * 100);
        console.log(`Chunk ${Math.floor(chunkStart/chunkSize) + 1} completed: ${chunkResults.length}/${chunk.length} items (${chunkProgress}%)`);
    }

    // إحصائيات نهائية
    const totalTime = performance.now() - performanceStats.startTime;
    const avgRequestTime = performanceStats.requestTimes.length > 0
        ? performanceStats.requestTimes.reduce((a, b) => a + b, 0) / performanceStats.requestTimes.length
        : 0;

    console.log(`Batch loading completed:`, {
        total: paramList.length,
        successful: results.length,
        failed: failedCount,
        totalTime: Math.round(totalTime),
        avgRequestTime: Math.round(avgRequestTime),
        requestsPerSecond: Math.round(paramList.length / (totalTime / 1000))
    });

    return results;
}

// دالة متقدمة لإنشاء Excel بسرعة عالية مع تحسينات
async function generateExcelUltraFast(data, worksheetName, columns, filename, options = {}) {
    const startTime = performance.now();

    try {
        // التحقق من صحة المدخلات
        if (!data || !Array.isArray(data)) {
            throw new Error('Invalid data: must be an array');
        }

        if (data.length === 0) {
            throw new Error('No data to export');
        }

        if (!worksheetName || typeof worksheetName !== 'string') {
            throw new Error('Invalid worksheet name');
        }

        if (!columns || !Array.isArray(columns) || columns.length === 0) {
            throw new Error('Invalid columns: must be a non-empty array');
        }

        if (!filename || typeof filename !== 'string') {
            throw new Error('Invalid filename');
        }

        // التحقق من توفر ExcelJS
        if (typeof ExcelJS === 'undefined') {
            throw new Error('ExcelJS library is not loaded');
        }

        console.log(`Starting Excel generation for ${data.length} rows...`);

        const workbook = new ExcelJS.Workbook();

        // تحسين إعدادات الـ workbook
        workbook.creator = 'Oditlz ETA Tool';
        workbook.lastModifiedBy = 'Oditlz Team';
        workbook.created = new Date();
        workbook.modified = new Date();

        const worksheet = workbook.addWorksheet(worksheetName);

        // تحسين إعدادات الـ worksheet
        worksheet.views = [{
            rightToLeft: true,
            rtl: true,
            activeCell: 'A1',
            state: "frozen",
            ySplit: 1,
            showGridLines: true,
            showRowColHeaders: true
        }];

        // تحسين الأعمدة
        worksheet.columns = columns.map(col => ({
            ...col,
            style: {
                font: { name: 'Calibri', size: 11 },
                alignment: { horizontal: 'right', vertical: 'middle' }
            }
        }));

        // تحسين إضافة البيانات - معالجة على دفعات
        const BATCH_SIZE = 1000;
        let addedRows = 0;

        for (let i = 0; i < data.length; i += BATCH_SIZE) {
            const batch = data.slice(i, i + BATCH_SIZE);
            worksheet.addRows(batch);
            addedRows += batch.length;

            // تحديث التقدم
            if (data.length > 1000) {
                const progress = Math.round(addedRows / data.length * 100);
                $(".downloadAllBtnText").html(`Creating Excel... ${progress}%`);
            }

            // تأخير قصير للسماح للواجهة بالتحديث
            if (i + BATCH_SIZE < data.length) {
                await new Promise(resolve => setTimeout(resolve, 10));
            }
        }

        // تحسين تنسيق الرأس
        const headerRow = worksheet.getRow(1);
        headerRow.font = { name: 'Calibri', size: 12, bold: true };
        headerRow.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'FFE6E6FA' }
        };
        headerRow.alignment = { horizontal: 'center', vertical: 'middle' };

        // إضافة فلاتر تلقائية
        if (data.length > 0) {
            worksheet.autoFilter = {
                from: 'A1',
                to: { row: data.length + 1, column: columns.length }
            };
        }

        // تحسين عرض الأعمدة تلقائياً (للأعمدة الصغيرة فقط)
        columns.forEach((col, index) => {
            const column = worksheet.getColumn(index + 1);
            if (col.width && col.width < 20) {
                column.width = col.width;
            }
        });

        console.log(`Excel structure created, generating buffer...`);
        $(".downloadAllBtnText").html("Finalizing Excel...");

        // إنشاء الملف مع ضغط محسن
        const buffer = await workbook.xlsx.writeBuffer({
            compression: 'DEFLATE',
            compressionLevel: 6
        });

        const blob = new Blob([buffer], {
            type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        });

        const endTime = performance.now();
        const totalTime = Math.round(endTime - startTime);

        console.log(`Excel generation completed:`, {
            rows: data.length,
            columns: columns.length,
            fileSize: Math.round(blob.size / 1024) + ' KB',
            totalTime: totalTime + ' ms',
            rowsPerSecond: Math.round(data.length / (totalTime / 1000))
        });

        saveAs(blob, filename);

    } catch (error) {
        // معالجة شاملة للأخطاء في إنشاء Excel
        const errorDetails = safeErrorHandler(error, 'generateExcelUltraFast');

        console.error('Error in generateExcelUltraFast:', {
            ...errorDetails,
            dataLength: data ? data.length : 'unknown',
            worksheetName: worksheetName,
            filename: filename,
            timestamp: new Date().toISOString()
        });

        // إظهار رسالة خطأ للمستخدم
        const userMessage = getUserMessageFromError(errorDetails.message);
        $(".downloadAllBtnText").html(userMessage);

        // إعادة تعيين الواجهة بعد تأخير
        setTimeout(() => {
            $(".downloadAllBtnText").html("Download All");
        }, 5000);

        throw new Error(`Excel generation failed: ${errorDetails.message}`);
    }
}

// دالة مساعدة لإنشاء Excel بسرعة (للتوافق مع الكود القديم)
async function generateExcelFast(data, worksheetName, columns, filename) {
    return await generateExcelUltraFast(data, worksheetName, columns, filename);
}
