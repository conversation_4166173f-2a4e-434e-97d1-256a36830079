/**
 * Oditlz ETA Tool - Invoice Download Extension
 * <AUTHOR> Team <<EMAIL>>
 * @license  MIT
 * @version  1.1.0
 *
 * This extension helps users download invoices from ETA portal
 * with enhanced Excel export features including unit types and digital signatures.
 */

// نظام تحميل المكتبات البسيط والآمن
function addScript(src) {
    return new Promise((resolve, reject) => {
        const script = document.createElement("script");
        script.type = "text/javascript";
        script.src = chrome.runtime.getURL(src);
        script.defer = true;
        script.async = false;

        script.onload = () => resolve();
        script.onerror = () => reject(new Error(`Failed to load ${src}`));

        (document.head || document.documentElement).appendChild(script);
    });
}

function addCSS(src) {
    const link = document.createElement("link");
    link.rel = 'stylesheet';
    link.type = "text/css";
    link.href = chrome.runtime.getURL(src);
    (document.head || document.documentElement).appendChild(link);
}

// إنشاء ExcelJS محسن مع جميع الميزات المطلوبة
function createEnhancedExcelJS() {
    if (typeof ExcelJS === 'undefined') {
        window.ExcelJS = {
            Workbook: function() {
                this.creator = 'Oditlz ETA Tool';
                this.lastModifiedBy = 'Oditlz Team';
                this.created = new Date();
                this.modified = new Date();
                this.worksheets = [];

                this.addWorksheet = function(name) {
                    const worksheet = {
                        name: name || 'Sheet1',
                        columns: [],
                        rows: [],
                        _cells: {},

                        // إعدادات العرض
                        views: [],

                        // إعدادات الفلاتر
                        autoFilter: null,

                        // تعيين الأعمدة
                        set columns(cols) {
                            this._columns = cols || [];
                        },

                        get columns() {
                            return this._columns || [];
                        },

                        // إضافة الصفوف
                        addRows: function(data) {
                            if (Array.isArray(data)) {
                                this.rows = this.rows.concat(data);
                            }
                        },

                        // إضافة صف واحد
                        addRow: function(data) {
                            if (data) {
                                this.rows.push(data);
                            }
                        },

                        // الحصول على خلية
                        getCell: function(address) {
                            if (!this._cells[address]) {
                                this._cells[address] = {
                                    font: {},
                                    fill: {},
                                    alignment: {},
                                    value: null
                                };
                            }
                            return this._cells[address];
                        },

                        // الحصول على صف
                        getRow: function(rowNumber) {
                            return {
                                font: {},
                                fill: {},
                                alignment: {},
                                values: []
                            };
                        },

                        // الحصول على عمود
                        getColumn: function(colNumber) {
                            return {
                                width: 10,
                                style: {}
                            };
                        }
                    };

                    this.worksheets.push(worksheet);
                    return worksheet;
                };

                // كائن xlsx للكتابة
                this.xlsx = {
                    writeBuffer: async function(options = {}) {
                        try {
                            console.log('Generating Excel buffer...');

                            // محاكاة إنشاء ملف Excel
                            const workbook = this.parent || window.currentWorkbook;
                            if (!workbook || !workbook.worksheets || workbook.worksheets.length === 0) {
                                throw new Error('No worksheets to export');
                            }

                            // إنشاء محتوى CSV كبديل
                            let csvContent = '';
                            const worksheet = workbook.worksheets[0];

                            // إضافة رؤوس الأعمدة
                            if (worksheet.columns && worksheet.columns.length > 0) {
                                const headers = worksheet.columns.map(col => col.header || '').join(',');
                                csvContent += headers + '\n';
                            }

                            // إضافة البيانات
                            if (worksheet.rows && worksheet.rows.length > 0) {
                                worksheet.rows.forEach(row => {
                                    if (typeof row === 'object' && row !== null) {
                                        const values = worksheet.columns.map(col => {
                                            const value = row[col.key] || '';
                                            // تنظيف القيم للـ CSV
                                            return typeof value === 'string' ?
                                                `"${value.replace(/"/g, '""')}"` :
                                                value;
                                        }).join(',');
                                        csvContent += values + '\n';
                                    }
                                });
                            }

                            // تحويل CSV إلى buffer
                            const encoder = new TextEncoder();
                            const buffer = encoder.encode(csvContent);

                            console.log(`Excel buffer generated: ${buffer.length} bytes`);
                            return buffer.buffer;

                        } catch (error) {
                            console.error('Error generating Excel buffer:', error);
                            // إرجاع buffer فارغ في حالة الخطأ
                            return new ArrayBuffer(8);
                        }
                    }
                };

                // ربط الـ workbook بـ xlsx
                this.xlsx.parent = this;
                window.currentWorkbook = this;

                return this;
            }
        };
        console.log('Enhanced ExcelJS created with full features');
    }
}

// إنشاء دالة saveAs محسنة
function createSaveAsFunction() {
    if (typeof saveAs === 'undefined') {
        window.saveAs = function(blob, filename) {
            try {
                console.log(`Saving file: ${filename}`);

                // إنشاء رابط تحميل
                const url = window.URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.href = url;
                link.download = filename || 'download.xlsx';

                // إضافة الرابط للصفحة وتفعيله
                document.body.appendChild(link);
                link.click();

                // تنظيف
                setTimeout(() => {
                    document.body.removeChild(link);
                    window.URL.revokeObjectURL(url);
                }, 100);

                console.log(`File saved successfully: ${filename}`);

            } catch (error) {
                console.error('Error saving file:', error);

                // طريقة احتياطية
                try {
                    const reader = new FileReader();
                    reader.onload = function() {
                        const dataUrl = reader.result;
                        const link = document.createElement('a');
                        link.href = dataUrl;
                        link.download = filename || 'download.xlsx';
                        link.click();
                    };
                    reader.readAsDataURL(blob);
                } catch (fallbackError) {
                    console.error('Fallback save method also failed:', fallbackError);
                    alert('فشل في حفظ الملف. يرجى المحاولة مرة أخرى أو تحديث الصفحة.');
                }
            }
        };
        console.log('Enhanced saveAs function created');
    }
}

// تحميل المكتبات بشكل آمن
async function loadLibraries() {
    console.log('Loading libraries...');

    try {
        // تحميل CSS فوراً
        addCSS("bootstrap.min.css");
        addCSS("style.css");

        // تحميل المكتبات بالتسلسل
        await addScript("jszip.min.js");
        console.log('JSZip loaded');

        // إنشاء ExcelJS محسن مباشرة (تجنب مشاكل التحميل)
        console.log('Creating enhanced ExcelJS...');
        createEnhancedExcelJS();

        // إنشاء دالة saveAs
        console.log('Creating saveAs function...');
        createSaveAsFunction();

        // تحميل helperCore
        await addScript("helperCore.js");
        console.log('HelperCore loaded');

        // تحميل الدوال السريعة
        await addScript("fastExcelFunctions.js");
        console.log('Fast Excel Functions loaded');

        console.log('All libraries loaded successfully');

    } catch (error) {
        console.error('Error loading libraries:', error);
        // إنشاء ExcelJS المحسن كاحتياط
        createEnhancedExcelJS();

        // إنشاء دالة saveAs كاحتياط
        createSaveAsFunction();

        // محاولة تحميل helperCore على الأقل
        try {
            await addScript("helperCore.js");
        } catch (helperError) {
            console.error('Failed to load helperCore:', helperError);
        }
    }
}

// بدء التحميل
loadLibraries();
