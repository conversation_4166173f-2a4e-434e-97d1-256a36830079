// نظام تحميل المكتبات المحسن
function addScript(src) {
    const script = document.createElement("script");
    script.type = "text/javascript";
    script.src = chrome.runtime.getURL(src);
    script.defer = "defer";
    (document.body || document.head || document.documentElement).appendChild(script);
}

function addCSS(src) {
    const link = document.createElement("link");
    link.rel = 'stylesheet';
    link.type = "text/css";
    link.href = chrome.runtime.getURL(src);
    (document.body || document.head || document.documentElement).appendChild(link);
}

// تحميل النظام المحسن أولاً
addScript("update_libraries.js");

// النظام الأساسي (احتياطي)
setTimeout(() => {
    // التحقق من تحميل النظام المحسن
    if (typeof loadLibrariesSequentially === 'undefined') {
        console.log('🔄 تحميل النظام الأساسي...');
        addScript("helperCore.js");
        addScript("jszip.min.js");
        addScript("polyfill.js");
        addScript("exceljs.min.js");
        addCSS("bootstrap.min.css");
        addCSS("style.css");
    }
}, 1000);
