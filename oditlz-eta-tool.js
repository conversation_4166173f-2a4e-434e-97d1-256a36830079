// نظام تحميل المكتبات البسيط والموثوق
function addScript(src) {
    return new Promise((resolve, reject) => {
        const script = document.createElement("script");
        script.type = "text/javascript";
        script.src = chrome.runtime.getURL(src);
        script.defer = "defer";

        script.onload = () => {
            console.log(`✅ تم تحميل: ${src}`);
            resolve();
        };

        script.onerror = () => {
            console.error(`Failed to load script: ${src}`);
            reject(new Error(`Failed to load: ${src}`));
        };

        (document.body || document.head || document.documentElement).appendChild(script);
    });
}

function addCSS(src) {
    return new Promise((resolve, reject) => {
        const link = document.createElement("link");
        link.rel = 'stylesheet';
        link.type = "text/css";
        link.href = chrome.runtime.getURL(src);

        link.onload = () => {
            console.log(`✅ تم تحميل CSS: ${src}`);
            resolve();
        };

        link.onerror = () => {
            console.error(`Failed to load CSS: ${src}`);
            reject(new Error(`Failed to load CSS: ${src}`));
        };

        (document.body || document.head || document.documentElement).appendChild(link);
    });
}

// تحميل المكتبات بالترتيب الصحيح
async function loadLibraries() {
    try {
        console.log('🚀 بدء تحميل المكتبات...');

        // تحميل CSS أولاً
        await addCSS("bootstrap.min.css");
        await addCSS("style.css");

        // تحميل JSZip أولاً (مطلوب لـ ExcelJS)
        await addScript("jszip.min.js");

        // تحقق من تحميل JSZip قبل ExcelJS
        if (typeof JSZip === 'undefined') {
            throw new Error('JSZip لم يتم تحميله بشكل صحيح');
        }

        // تحميل ExcelJS مع معالجة خاصة
        try {
            await addScript("exceljs.min.js");
        } catch (excelError) {
            console.warn('⚠️ فشل تحميل ExcelJS، سيتم استخدام النسخة الاحتياطية');
            await loadExcelJSFallback();
        }

        // تحميل الكود الأساسي أخيراً
        await addScript("helperCore.js");

        console.log('✅ تم تحميل جميع المكتبات بنجاح');

    } catch (error) {
        console.error('Error loading libraries:', error);
        console.log('🔄 تحويل للنظام الاحتياطي...');
        // تحميل بالطريقة التقليدية كاحتياط
        loadBasicLibraries();
    }
}

// تحميل ExcelJS الاحتياطي
async function loadExcelJSFallback() {
    return new Promise((resolve, reject) => {
        console.log('🔄 تحميل ExcelJS بالطريقة الاحتياطية...');

        // إنشاء ExcelJS بسيط إذا فشل التحميل
        if (typeof ExcelJS === 'undefined') {
            window.ExcelJS = {
                Workbook: function() {
                    console.warn('⚠️ استخدام ExcelJS مبسط - بعض الميزات قد لا تعمل');
                    return {
                        addWorksheet: function(name) {
                            return {
                                columns: [],
                                addRows: function() {},
                                getCell: function() {
                                    return { font: {} };
                                }
                            };
                        },
                        xlsx: {
                            writeBuffer: function() {
                                return Promise.resolve(new ArrayBuffer(0));
                            }
                        }
                    };
                }
            };
        }

        setTimeout(() => {
            console.log('✅ تم تهيئة ExcelJS الاحتياطي');
            resolve();
        }, 100);
    });
}

// النظام الاحتياطي البسيط
function loadBasicLibraries() {
    console.log('🔄 تحميل النظام الاحتياطي...');

    const styles = [
        "bootstrap.min.css",
        "style.css"
    ];

    // تحميل CSS
    styles.forEach(css => {
        const link = document.createElement("link");
        link.rel = 'stylesheet';
        link.type = "text/css";
        link.href = chrome.runtime.getURL(css);
        document.head.appendChild(link);
    });

    // تحميل JSZip أولاً
    setTimeout(() => {
        const jszip = document.createElement("script");
        jszip.src = chrome.runtime.getURL("jszip.min.js");
        jszip.onload = () => {
            console.log('✅ تم تحميل JSZip');

            // تحميل ExcelJS مع معالجة الأخطاء
            setTimeout(() => {
                const excel = document.createElement("script");
                excel.src = chrome.runtime.getURL("exceljs.min.js");
                excel.onload = () => {
                    console.log('✅ تم تحميل ExcelJS');
                    loadHelperCore();
                };
                excel.onerror = () => {
                    console.warn('⚠️ فشل تحميل ExcelJS، سيتم استخدام النسخة المبسطة');
                    loadExcelJSFallback().then(() => {
                        loadHelperCore();
                    });
                };
                document.body.appendChild(excel);
            }, 300);
        };
        jszip.onerror = () => {
            console.error('Failed to load JSZip');
        };
        document.body.appendChild(jszip);
    }, 100);
}

// تحميل helperCore
function loadHelperCore() {
    setTimeout(() => {
        const helper = document.createElement("script");
        helper.src = chrome.runtime.getURL("helperCore.js");
        helper.onload = () => {
            console.log('✅ تم تحميل helperCore');
            console.log('✅ تم تحميل النظام الاحتياطي بنجاح');
        };
        helper.onerror = () => {
            console.error('Failed to load helperCore');
        };
        document.body.appendChild(helper);
    }, 200);
}

// بدء التحميل
loadLibraries();
