// نظام تحميل المكتبات البسيط
function addScript(src) {
    const script = document.createElement("script");
    script.type = "text/javascript";
    script.src = chrome.runtime.getURL(src);
    script.defer = "defer";
    (document.body || document.head || document.documentElement).appendChild(script);
}

function addCSS(src) {
    const link = document.createElement("link");
    link.rel = 'stylesheet';
    link.type = "text/css";
    link.href = chrome.runtime.getURL(src);
    (document.body || document.head || document.documentElement).appendChild(link);
}

// تحميل المكتبات بشكل بسيط
function loadLibraries() {
    console.log('Loading libraries...');

    // تحميل CSS
    addCSS("bootstrap.min.css");
    addCSS("style.css");

    // تحميل JavaScript مع تأخير بسيط
    setTimeout(() => addScript("jszip.min.js"), 100);
    setTimeout(() => addScript("exceljs.min.js"), 300);
    setTimeout(() => addScript("helperCore.js"), 500);

    console.log('Libraries loading initiated...');
}

// بدء التحميل
loadLibraries();
