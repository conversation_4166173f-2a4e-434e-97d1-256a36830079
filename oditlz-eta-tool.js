// نظام تحميل المكتبات البسيط والموثوق
function addScript(src) {
    return new Promise((resolve, reject) => {
        const script = document.createElement("script");
        script.type = "text/javascript";
        script.src = chrome.runtime.getURL(src);
        script.defer = "defer";

        script.onload = () => {
            console.log(`✅ تم تحميل: ${src}`);
            resolve();
        };

        script.onerror = () => {
            console.error(`❌ فشل تحميل: ${src}`);
            reject(new Error(`فشل في تحميل: ${src}`));
        };

        (document.body || document.head || document.documentElement).appendChild(script);
    });
}

function addCSS(src) {
    return new Promise((resolve, reject) => {
        const link = document.createElement("link");
        link.rel = 'stylesheet';
        link.type = "text/css";
        link.href = chrome.runtime.getURL(src);

        link.onload = () => {
            console.log(`✅ تم تحميل CSS: ${src}`);
            resolve();
        };

        link.onerror = () => {
            console.error(`❌ فشل تحميل CSS: ${src}`);
            reject(new Error(`فشل في تحميل CSS: ${src}`));
        };

        (document.body || document.head || document.documentElement).appendChild(link);
    });
}

// تحميل المكتبات بالترتيب الصحيح
async function loadLibraries() {
    try {
        console.log('🚀 بدء تحميل المكتبات...');

        // تحميل CSS أولاً
        await addCSS("bootstrap.min.css");
        await addCSS("style.css");

        // تحميل المكتبات الأساسية
        await addScript("jszip.min.js");
        await addScript("exceljs.min.js");

        // تحميل الكود الأساسي أخيراً
        await addScript("helperCore.js");

        console.log('✅ تم تحميل جميع المكتبات بنجاح');

    } catch (error) {
        console.error('❌ خطأ في تحميل المكتبات:', error);
        // تحميل بالطريقة التقليدية كاحتياط
        loadBasicLibraries();
    }
}

// النظام الاحتياطي البسيط
function loadBasicLibraries() {
    console.log('🔄 تحميل النظام الاحتياطي...');

    const libraries = [
        "jszip.min.js",
        "exceljs.min.js",
        "helperCore.js"
    ];

    const styles = [
        "bootstrap.min.css",
        "style.css"
    ];

    // تحميل CSS
    styles.forEach(css => {
        const link = document.createElement("link");
        link.rel = 'stylesheet';
        link.type = "text/css";
        link.href = chrome.runtime.getURL(css);
        document.head.appendChild(link);
    });

    // تحميل JavaScript مع تأخير بسيط
    libraries.forEach((lib, index) => {
        setTimeout(() => {
            const script = document.createElement("script");
            script.type = "text/javascript";
            script.src = chrome.runtime.getURL(lib);
            script.defer = "defer";
            document.body.appendChild(script);
        }, index * 200);
    });
}

// بدء التحميل
loadLibraries();
