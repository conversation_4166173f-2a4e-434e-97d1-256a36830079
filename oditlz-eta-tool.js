/**
 * Oditlz ETA Tool - Invoice Download Extension
 * <AUTHOR> Team <<EMAIL>>
 * @license  MIT
 * @version  1.1.0
 *
 * This extension helps users download invoices from ETA portal
 * with enhanced Excel export features including unit types and digital signatures.
 */

// نظام تحميل المكتبات البسيط والآمن
function addScript(src) {
    return new Promise((resolve, reject) => {
        const script = document.createElement("script");
        script.type = "text/javascript";
        script.src = chrome.runtime.getURL(src);
        script.defer = true;
        script.async = false;

        script.onload = () => resolve();
        script.onerror = () => reject(new Error(`Failed to load ${src}`));

        (document.head || document.documentElement).appendChild(script);
    });
}

function addCSS(src) {
    const link = document.createElement("link");
    link.rel = 'stylesheet';
    link.type = "text/css";
    link.href = chrome.runtime.getURL(src);
    (document.head || document.documentElement).appendChild(link);
}

// إنشاء ExcelJS مبسط
function createSimpleExcelJS() {
    if (typeof ExcelJS === 'undefined') {
        window.ExcelJS = {
            Workbook: function() {
                this.worksheets = [];

                this.addWorksheet = function(name) {
                    const worksheet = {
                        name: name || 'Sheet1',
                        columns: [],
                        rows: [],

                        addRows: function(data) {
                            if (Array.isArray(data)) {
                                this.rows = this.rows.concat(data);
                            }
                        },

                        getCell: function(address) {
                            return {
                                font: {},
                                value: null
                            };
                        }
                    };

                    this.worksheets.push(worksheet);
                    return worksheet;
                };

                this.xlsx = {
                    writeBuffer: function() {
                        return Promise.resolve(new ArrayBuffer(8));
                    }
                };

                return this;
            }
        };
        console.log('Simple ExcelJS created');
    }
}

// تحميل المكتبات بشكل آمن
async function loadLibraries() {
    console.log('Loading libraries...');

    try {
        // تحميل CSS فوراً
        addCSS("bootstrap.min.css");
        addCSS("style.css");

        // تحميل المكتبات بالتسلسل
        await addScript("jszip.min.js");
        console.log('JSZip loaded');

        // إنشاء ExcelJS مبسط مباشرة (تجنب مشاكل التحميل)
        console.log('Creating simple ExcelJS...');
        createSimpleExcelJS();

        // تحميل helperCore
        await addScript("helperCore.js");
        console.log('HelperCore loaded');

        console.log('All libraries loaded successfully');

    } catch (error) {
        console.error('Error loading libraries:', error);
        // إنشاء ExcelJS البسيط كاحتياط
        createSimpleExcelJS();

        // محاولة تحميل helperCore على الأقل
        try {
            await addScript("helperCore.js");
        } catch (helperError) {
            console.error('Failed to load helperCore:', helperError);
        }
    }
}

// بدء التحميل
loadLibraries();
