// ملف تحديث المكتبات - update_libraries.js
// هذا الملف يحتوي على التحسينات المقترحة للمكتبات

// تحسين تحميل المكتبات مع معالجة الأخطاء
function addScriptWithFallback(src, fallbackSrc = null) {
    return new Promise((resolve, reject) => {
        const script = document.createElement("script");
        script.type = "text/javascript";
        script.defer = "defer";
        
        script.onload = () => {
            console.log(`✅ تم تحميل المكتبة بنجاح: ${src}`);
            resolve();
        };
        
        script.onerror = () => {
            console.warn(`⚠️ فشل تحميل المكتبة: ${src}`);
            if (fallbackSrc) {
                console.log(`🔄 محاولة تحميل النسخة الاحتياطية: ${fallbackSrc}`);
                addScriptWithFallback(fallbackSrc, null).then(resolve).catch(reject);
            } else {
                reject(new Error(`فشل في تحميل المكتبة: ${src}`));
            }
        };
        
        script.src = chrome.runtime.getURL(src);
        (document.body || document.head || document.documentElement).appendChild(script);
    });
}

// تحسين تحميل CSS مع معالجة الأخطاء
function addCSSWithFallback(src, fallbackSrc = null) {
    return new Promise((resolve, reject) => {
        const link = document.createElement("link");
        link.rel = 'stylesheet';
        link.type = "text/css";
        
        link.onload = () => {
            console.log(`✅ تم تحميل CSS بنجاح: ${src}`);
            resolve();
        };
        
        link.onerror = () => {
            console.warn(`⚠️ فشل تحميل CSS: ${src}`);
            if (fallbackSrc) {
                console.log(`🔄 محاولة تحميل النسخة الاحتياطية: ${fallbackSrc}`);
                addCSSWithFallback(fallbackSrc, null).then(resolve).catch(reject);
            } else {
                reject(new Error(`فشل في تحميل CSS: ${src}`));
            }
        };
        
        link.href = chrome.runtime.getURL(src);
        (document.body || document.head || document.documentElement).appendChild(link);
    });
}

// تحميل المكتبات بالترتيب الصحيح
async function loadLibrariesSequentially() {
    try {
        console.log('🚀 بدء تحميل المكتبات المحسنة...');

        // تحميل CSS الأساسي
        await addCSSWithFallback("bootstrap.min.css");
        await addCSSWithFallback("style.css");

        // تحميل المكتبات الأساسية المطلوبة
        await addScriptWithFallback("jszip.min.js");
        await addScriptWithFallback("exceljs.min.js");

        // تحميل الكود الأساسي أخيراً
        await addScriptWithFallback("helperCore.js");

        console.log('✅ تم تحميل المكتبات الأساسية بنجاح');

        // تهيئة المكتبات
        initializeLibraries();

    } catch (error) {
        console.error('❌ خطأ في تحميل المكتبات:', error);
        // تحميل النسخة الأساسية في حالة الفشل
        throw error; // إعادة رمي الخطأ للمعالجة في المستوى الأعلى
    }
}

// تهيئة المكتبات بعد التحميل
function initializeLibraries() {
    // تحسين إعدادات ExcelJS
    if (typeof ExcelJS !== 'undefined') {
        console.log('✅ تم تهيئة ExcelJS');
    }

    // تحسين إعدادات JSZip
    if (typeof JSZip !== 'undefined') {
        // إعدادات افتراضية للضغط
        JSZip.defaults = {
            ...JSZip.defaults,
            compression: "DEFLATE",
            compressionOptions: { level: 6 }
        };
        console.log('✅ تم تهيئة JSZip');
    }
}

// تحميل النسخة الأساسية في حالة الفشل
function loadBasicLibraries() {
    console.log('🔄 تحميل النسخة الأساسية...');

    try {
        // النسخة الأساسية القديمة
        const basicLibraries = [
            "jszip.min.js",
            "exceljs.min.js",
            "helperCore.js"
        ];

        const basicCSS = [
            "bootstrap.min.css",
            "style.css"
        ];

        basicLibraries.forEach((lib, index) => {
            setTimeout(() => {
                const script = document.createElement("script");
                script.type = "text/javascript";
                script.src = chrome.runtime.getURL(lib);
                script.defer = "defer";
                script.onload = () => console.log(`✅ تم تحميل: ${lib}`);
                script.onerror = () => console.error(`❌ فشل تحميل: ${lib}`);
                (document.body || document.head || document.documentElement).appendChild(script);
            }, index * 100); // تأخير بسيط بين المكتبات
        });

        basicCSS.forEach(css => {
            const link = document.createElement("link");
            link.rel = 'stylesheet';
            link.type = "text/css";
            link.href = chrome.runtime.getURL(css);
            link.onload = () => console.log(`✅ تم تحميل CSS: ${css}`);
            link.onerror = () => console.error(`❌ فشل تحميل CSS: ${css}`);
            (document.body || document.head || document.documentElement).appendChild(link);
        });

        console.log('🔄 تم بدء تحميل النسخة الأساسية');

    } catch (error) {
        console.error('❌ خطأ في تحميل النسخة الأساسية:', error);
    }
}

// مراقبة أداء المكتبات
function monitorPerformance() {
    if (performance && performance.mark) {
        performance.mark('libraries-load-start');
        
        window.addEventListener('load', () => {
            performance.mark('libraries-load-end');
            performance.measure('libraries-load-time', 'libraries-load-start', 'libraries-load-end');
            
            const measure = performance.getEntriesByName('libraries-load-time')[0];
            console.log(`📊 وقت تحميل المكتبات: ${measure.duration.toFixed(2)}ms`);
        });
    }
}

// تحسين معالجة الذاكرة
function optimizeMemoryUsage() {
    // تنظيف المتغيرات غير المستخدمة
    window.addEventListener('beforeunload', () => {
        // تنظيف المتغيرات العامة
        if (window.invoiceList) {
            window.invoiceList.length = 0;
        }
        if (window.receiptList) {
            window.receiptList.length = 0;
        }
        if (window.codeList) {
            window.codeList.length = 0;
        }
    });
}

// تحسين معالجة الأخطاء
function setupErrorHandling() {
    window.addEventListener('error', (event) => {
        console.error('خطأ في التطبيق:', {
            message: event.message,
            filename: event.filename,
            lineno: event.lineno,
            colno: event.colno,
            error: event.error
        });
    });
    
    window.addEventListener('unhandledrejection', (event) => {
        console.error('Promise مرفوض:', event.reason);
    });
}

// دالة التحقق من توفر المكتبات
function checkLibrariesAvailability() {
    const requiredLibraries = {
        'JSZip': typeof JSZip !== 'undefined',
        'ExcelJS': typeof ExcelJS !== 'undefined'
    };

    const missingLibraries = Object.entries(requiredLibraries)
        .filter(([name, available]) => !available)
        .map(([name]) => name);

    if (missingLibraries.length > 0) {
        console.warn('⚠️ مكتبات مفقودة:', missingLibraries);
        return false;
    }

    console.log('✅ جميع المكتبات المطلوبة متوفرة');
    return true;
}

// تحسين تحميل الملفات الكبيرة
function optimizeFileHandling() {
    // تحسين معالجة الملفات الكبيرة
    if (typeof JSZip !== 'undefined') {
        JSZip.support.blob = true;
        JSZip.support.uint8array = true;
    }
}

// بدء التحميل المحسن
(function() {
    console.log('🔧 تهيئة النظام المحسن...');

    // إعداد مراقبة الأداء
    monitorPerformance();

    // إعداد معالجة الأخطاء
    setupErrorHandling();

    // تحسين الذاكرة
    optimizeMemoryUsage();

    // بدء تحميل المكتبات
    loadLibrariesSequentially().catch(error => {
        console.error('❌ فشل في تحميل المكتبات المحسنة:', error);
        loadBasicLibraries();
    });

    // التحقق الدوري من المكتبات
    setTimeout(() => {
        if (!checkLibrariesAvailability()) {
            console.log('🔄 إعادة محاولة تحميل المكتبات...');
            loadBasicLibraries();
        } else {
            optimizeFileHandling();
        }
    }, 2000);
})();

// تصدير الدوال للاستخدام الخارجي
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        loadLibrariesSequentially,
        checkLibrariesAvailability,
        optimizeFileHandling
    };
}
