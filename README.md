# Oditlz ETA Tool

**Author:** Oditlz Team <<EMAIL>>
**License:** MIT  
**Version:** 1.1.0

## Overview

Oditlz ETA Tool is a Chrome extension that enhances the Egyptian Tax Authority (ETA) invoicing portal by providing advanced download and export capabilities. The extension allows users to download invoices and receipts as Excel files with enhanced features including unit types and digital signature information.

## Features

### 🚀 Core Features
- **One-click download** of all invoices and receipts
- **Excel export** with comprehensive data
- **Enhanced invoice details** including unit types and digital signatures
- **Batch processing** for multiple documents
- **Real-time progress tracking**

### 📊 Excel Export Enhancements
- **Unit Type Column** - Shows measurement units for each item
- **Digital Signature Status** - Displays signature verification status
- **Comprehensive Data** - All invoice/receipt fields included
- **Professional Formatting** - Clean, readable Excel output

### 🛡️ Security & Reliability
- **Content Security Policy compliant**
- **Fallback systems** for maximum reliability
- **Error handling** for all scenarios
- **Safe library loading** with backup options

## Installation

1. Download the extension files
2. Open Chrome and go to `chrome://extensions/`
3. Enable "Developer mode"
4. Click "Load unpacked" and select the extension folder
5. Navigate to the ETA portal and start using the enhanced features

## Usage

### Basic Usage
1. Log in to the ETA invoicing portal
2. Navigate to any invoice or receipt list
3. Look for the "Download All" button
4. Click to download all visible items as Excel

### Excel Features
The exported Excel files include:
- **Invoice Details**: All standard fields plus unit types and signatures
- **Receipt Details**: Complete receipt information with enhancements
- **Professional Formatting**: Clean, readable layout
- **Arabic Support**: Full RTL and Arabic text support

## Technical Details

### Architecture
- **Manifest V3** Chrome extension
- **Content Script** injection for portal integration
- **Modular Design** with separate core and UI components
- **Fallback Systems** for maximum compatibility

### Files Structure
```
├── manifest.json           # Extension configuration
├── Oditlz-ETA-Tool.js      # Main content script and library loader
├── helperCore.js           # Core functionality and Excel export
├── jszip.min.js           # ZIP compression library
├── exceljs.min.js         # Excel generation library (optional)
├── bootstrap.min.css      # UI styling
├── style.css              # Custom styles
└── README.md              # This file
```

### Libraries Used
- **JSZip** - For file compression
- **ExcelJS** - For Excel file generation (with fallback)
- **Bootstrap** - For UI components
- **jQuery** - For DOM manipulation (loaded from portal)

## Enhanced Features

### Unit Type Support
The extension now includes unit type information for each invoice line item:
- Extracts from `unitType`, `unitOfMeasure`, or `unit` fields
- Displays in dedicated column in Excel export
- Supports all common unit types (kg, piece, meter, liter, etc.)

### Digital Signature Information
Digital signature status is now included in exports:
- **Signature Status**: "موقع رقمياً" (Digitally Signed) or "غير موقع" (Not Signed)
- **Verification Status**: "صالح" (Valid) or "غير متوفر" (Not Available)
- Extracted from document signature data

## Browser Compatibility

- **Chrome** 88+ (Primary support)
- **Edge** 88+ (Chromium-based)
- **Other Chromium browsers** (Limited testing)

## Development

### Building from Source
1. Clone the repository
2. Ensure all files are present
3. Load as unpacked extension in Chrome
4. Test on ETA portal

### Contributing
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## Troubleshooting

### Common Issues

#### Extension not loading
- Check that all files are present
- Verify manifest.json is valid
- Ensure Chrome developer mode is enabled

#### Download not working
- Check browser console for errors
- Verify you're on the correct ETA portal page
- Try refreshing the page

#### Excel files empty or corrupted
- Check that JSZip library loaded correctly
- Verify ExcelJS fallback is working
- Look for console error messages

### Debug Information
Open Chrome DevTools (F12) and check the Console tab for detailed error messages and loading status.

## License

MIT License - see LICENSE file for details.

## Support

For support and questions:
- Visit: https://oditlz.com
- Email: <EMAIL>

## Changelog

### Version 1.1.0
- Added unit type support in Excel exports
- Added digital signature status information
- Improved error handling and fallback systems
- Enhanced Content Security Policy compliance
- Simplified library loading system

### Version 1.0.0
- Initial release
- Basic invoice and receipt download functionality
- Excel export capabilities
- ETA portal integration

## Credits

**Developed by:** Oditlz Team  
**Website:** https://oditlz.com

### Third-party Libraries
- JSZip by Stuart Knightley and contributors
- ExcelJS by Guyon Roche and contributors
- Bootstrap by Twitter, Inc.

## Disclaimer

This extension is not affiliated with the Egyptian Tax Authority. It is an independent tool designed to enhance user experience with the ETA portal. Users are responsible for ensuring compliance with all applicable laws and regulations.

---

**© 2024 Oditlz Team. All rights reserved.**
